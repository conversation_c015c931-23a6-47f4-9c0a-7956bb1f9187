# BRC20 内存池监控系统

一个高性能的 BRC20 代币内存池监控和数据分析系统，提供实时监听、智能缓存和丰富的API接口。

## 🚀 系统特性

### 核心功能
- **实时内存池监听**: 监控比特币内存池中的BRC20交易
- **自动区块扫描**: 自动检测新区块并扫描其中的BRC20铭文
- **智能缓存系统**: 高性能缓存机制，毫秒级API响应
- **费率分析**: 智能费率区间分析和统计
- **代币信息管理**: 集成UniSat API的代币信息查询

### 技术特点
- **高并发**: 多线程架构，支持并发处理
- **容错性强**: 完善的错误处理和自动重试机制
- **内存优化**: 智能缓存清理，防止内存泄露
- **配置灵活**: 支持多种配置选项和日志级别

## 📋 系统架构

```
BRC20监控系统
├── 内存池监听线程 (mempool_listener)
│   ├── 实时监听内存池交易
│   ├── 解析BRC20铭文内容
│   └── 计算交易费率和统计
├── 区块监听线程 (scan_recent_brc20_blocks)
│   ├── 自动检测新区块
│   ├── 扫描区块中的BRC20交易
│   └── 更新区块缓存
├── 数据缓存系统
│   ├── 内存池交易缓存 (inscriptions_cache)
│   ├── 区块数据缓存 (block_cache)
│   └── 代币信息缓存 (tick_info_cache)
└── REST API服务
    ├── 搜索接口
    ├── 趋势分析接口
    ├── 费率统计接口
    └── 代币信息接口
```

## ⚙️ 配置说明

### config.json 配置文件

```json
{
    "bitcoin_node": {
        "rpc_user": "__cookie__",
        "rpc_password": "your_rpc_password",
        "rpc_host": "*************",
        "rpc_port": "8082"
    },
    "brc20_api": {
        "url": "https://open-api.unisat.io",
        "path": "/v1/indexer/brc20",
        "authorization": "Bearer your_api_token"
    },
    "server": {
        "host": "0.0.0.0",
        "port": 5001,
        "comment": "API服务器配置，host为监听地址，port为监听端口"
    },
    "logging": {
        "level": "WARNING",
        "comment": "日志等级: DEBUG, INFO, WARNING, ERROR, CRITICAL. 生产环境建议使用WARNING或ERROR"
    }
}
```

### 配置参数说明

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `bitcoin_node.rpc_*` | Bitcoin节点RPC连接配置 | - |
| `brc20_api.url` | UniSat API 基础URL | https://open-api.unisat.io |
| `brc20_api.authorization` | UniSat API 授权Token | - |
| `server.host` | API服务监听地址 | 0.0.0.0 |
| `server.port` | API服务监听端口 | 5001 |
| `logging.level` | 日志级别 | WARNING |

## 🔌 API接口文档

### 1. 搜索铭文内容

**接口**: `GET /api/search_inscriptions`

**描述**: 搜索内存池中的铭文内容

**参数**:
- `content` (string, required): 要搜索的内容
- `type` (string, optional): 铭文类型过滤

**响应示例**:
```json
{
    "total": 10,
    "results": [
        {
            "txid": "abc123...",
            "type": "text/plain",
            "content": "{\\"p\\":\\"brc-20\\",\\"op\\":\\"mint\\",\\"tick\\":\\"ordi\\",\\"amt\\":\\"1000\\"}",
            "output_address": "bc1q...",
            "fee": 15.2
        }
    ]
}
```

### 2. 获取BRC20代币交易

**接口**: `GET /api/brc20/tick/{tick}`

**描述**: 获取指定BRC20代币的所有内存池交易

**参数**:
- `tick` (string): BRC20代币标识符

**响应示例**:
```json
{
    "total": 5,
    "results": [
        {
            "txid": "def456...",
            "tick": "ordi",
            "operation": "mint",
            "amount": "1000",
            "fee": 12.5,
            "output_address": "bc1q..."
        }
    ]
}
```

### 3. 获取热门BRC20代币

**接口**: `GET /api/brc20/trending`

**描述**: 获取当前内存池中最热门的BRC20代币

**参数**:
- `limit` (int, optional): 返回数量，默认20，范围1-100

**响应示例**:
```json
{
    "total": 15,
    "results": [
        {
            "tick": "ordi",
            "total_txs": 156,
            "total_amount": "156000",
            "unique_addresses": 45,
            "median_fee": 10.5,
            "inscriptionId": "abc123...i0",
            "supply": "21000000",
            "minted": "15000000",
            "remaining_mint": "6000000",
            "remaining_mint_count": 6000
        }
    ]
}
```

### 4. 获取交易费率排名

**接口**: `GET /api/brc20/rank/{tick}/{txid}`

**描述**: 获取指定交易在该代币中的费率排名

**参数**:
- `tick` (string): BRC20代币标识符
- `txid` (string): 交易ID

**响应示例**:
```json
{
    "txid": "ghi789...",
    "tick": "ordi",
    "rank": 5,
    "total": 156,
    "fee": 25.3
}
```

### 5. 获取费率分布

**接口**: `GET /api/brc20/tick/{tick}/distribution`

**描述**: 获取指定代币的费率分布统计

**参数**:
- `tick` (string): BRC20代币标识符

**响应示例**:
```json
{
    "0.2-0.5": 100,
    "0.5-1.0": 45,
    "1.0-5.0": 10,
    "5.0-20.0": 1
}
```

### 6. 获取代币详细信息

**接口**: `GET /api/brc20/tick/{tick}/info`

**描述**: 获取BRC20代币的详细信息和统计数据

**参数**:
- `tick` (string): BRC20代币标识符
- `force_update` (bool, optional): 强制从外部API更新
- `fee` (string, optional): 费率范围，格式为"min,max"
- `nTx` (int, optional): 最大返回交易数量

**响应示例**:
```json
{
    "inscriptionId": "abc123...i0",
    "inscriptionNumber": 12345,
    "supply": "21000000",
    "supply_raw": "2100000000000000000000000",
    "minted": "15000000",
    "minted_raw": "1500000000000000000000000",
    "decimal": 18,
    "limitPerMint": "1000",
    "limitPerMint_raw": "100000000000000000000",
    "remaining_mint": "6000000",
    "remaining_mint_count": 6000,
    "selfMint": false,
    "deployBy": {"address": "bc1q..."},
    "deployHeight": 800000,
    "deployBlocktime": 1700000000,
    "fee_distribution": {
        "0.2-0.5": 100,
        "0.5-1.0": 45
    },
    "unique_addresses": 45,
    "median_fee": 10.5,
    "total_txs": 156,
    "total_amount": "156000",
    "pperchain": 50,
    "pperchain_fee": 1.0
}
```

### 7. 获取最近区块的BRC20数据

**接口**: `GET /api/brc20/recent`

**描述**: 获取最近几个区块的BRC20铭文铸造记录

**参数**:
- `limit` (int, optional): 返回区块数量，默认6，范围1-20

**响应示例**:
```json
{
    "recent_blocks": [
        {
            "block_height": 867892,
            "brc20_count": 3,
            "brc20_stats": [
                {
                    "tick": "ordi",
                    "tick_name": "ordi",
                    "tx_count": 120,
                    "address_count": 35,
                    "total_amount": "120000",
                    "median_fee": 12.5
                }
            ]
        }
    ]
}
```

### 8. 获取指定区块的BRC20数据

**接口**: `GET /api/brc20/block/{block_height}`

**描述**: 获取指定区块中的BRC20铭文交易记录

**参数**:
- `block_height` (int): 区块高度

**响应示例**:
```json
{
    "block_height": 867890,
    "brc20_count": 2,
    "brc20_stats": [
        {
            "tick": "ordi",
            "tick_name": "ordi", 
            "tx_count": 85,
            "address_count": 25,
            "total_amount": "85000",
            "median_fee": 15.2
        }
    ]
}
```

## 🚀 快速开始

### 1. 环境要求
- Python 3.8+
- Bitcoin Core节点 (RPC访问)
- UniSat API Token

### 2. 安装依赖
```bash
pip install fastapi uvicorn requests httpx pydantic numpy json5
```

### 3. 配置文件
创建 `config.json` 配置文件，参考上面的配置说明。

### 4. 启动服务
```bash
python brc20.py
```

### 5. 访问API
服务启动后，可以通过以下方式访问：
- API基础地址: `http://localhost:5001`
- API文档: `http://localhost:5001/docs`

## 📊 监控和日志

### 日志级别配置
- **DEBUG**: 详细调试信息 (交易数量、缓存操作)
- **INFO**: 重要状态信息 (应用启动、新交易)
- **WARNING**: 需要注意的问题 (缺失tick、API错误)
- **ERROR**: 严重错误 (RPC失败、配置错误)
- **CRITICAL**: 系统级错误

### 监控指标
系统自动监控以下指标：
- 内存池交易数量
- BRC20交易统计
- 缓存命中率
- API响应时间
- 区块扫描进度

## 🔧 性能优化

### 缓存策略
- **内存池缓存**: 实时更新，自动清理过期交易
- **区块缓存**: 最多保留50个区块，智能LRU清理
- **代币信息缓存**: 30秒过期时间，避免频繁API调用

### 并发处理
- 内存池监听线程: 处理实时交易
- 区块扫描线程: 处理历史数据
- API服务线程: 处理HTTP请求

### 内存管理
- 自动清理过期数据
- 限制缓存大小
- 智能垃圾回收

## 🛠️ 故障排除

### 常见问题

1. **RPC连接失败**
   - 检查Bitcoin节点是否运行
   - 验证RPC配置是否正确
   - 确认网络连接

2. **API响应慢**
   - 检查日志级别设置
   - 确认缓存是否正常工作
   - 查看系统资源使用情况

3. **代币信息获取失败**
   - 验证UniSat API Token
   - 检查网络连接
   - 确认API配额是否充足

### 调试模式
设置日志级别为 `DEBUG` 以获取详细信息：
```json
{
    "logging": {
        "level": "DEBUG"
    }
}
```

## 📈 扩展开发

### 添加新功能
1. 创建新的API路由
2. 实现数据处理逻辑
3. 添加适当的缓存机制
4. 编写单元测试

### 自定义配置
系统支持扩展配置选项，可以在 `config.json` 中添加自定义参数。

## 📝 版本历史

### v2.0.0 (当前版本)
- ✅ 添加自动区块监听
- ✅ 优化缓存机制
- ✅ 增强API功能
- ✅ 改进错误处理
- ✅ 支持费率范围查询

### v1.0.0 
- ✅ 基础内存池监听
- ✅ BRC20铭文解析
- ✅ REST API接口
- ✅ 基础缓存功能

## 📄 许可证

本项目采用 MIT 许可证。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

---

**联系方式**: 如有问题，请通过Issue联系我们。 