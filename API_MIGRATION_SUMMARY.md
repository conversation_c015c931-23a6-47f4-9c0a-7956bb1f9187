# BRC20 API 迁移总结

## 📋 更改概述

已成功将 BRC20 API 从 Unisat API 迁移到新的自定义API接口。

## 🔧 具体更改

### 1. 配置文件更改 (config.json)

**旧配置:**
```json
"brc20_api": {
    "url": "https://open-api.unisat.io",
    "path": "/v1/indexer/brc20",
    "authorization": "Bearer 81ca36b46819b911e5447f7853c78f01df26170c98fb31f4e78595c162049975"
}
```

**新配置:**
```json
"brc20_api": {
    "url": "http://47.242.55.181:3340",
    "path": "/v1/brc20/tick_info",
    "authorization": ""
}
```

### 2. 主程序更改 (brc20.py)

#### 2.1 默认配置更新
- 更新了 `load_config()` 函数中的默认配置
- 新API不需要授权头

#### 2.2 URL构建方式更改
**旧方式:**
```python
full_url = f"{brc20_api_url}{brc20_api_path}/{encoded_tick}/info"
```

**新方式:**
```python
full_url = f"{brc20_api_url}{brc20_api_path}?ticker={encoded_tick}"
```

#### 2.3 授权头处理
**旧方式:**
```python
headers = {
    "Authorization": brc20_api_auth
}
```

**新方式:**
```python
headers = {}
if brc20_api_auth:
    headers["Authorization"] = brc20_api_auth
```

#### 2.4 响应数据解析更改
**旧格式检查:**
```python
if data.get("code") == 0 and "data" in data:
    original_data = data["data"]
```

**新格式检查:**
```python
if data.get("error") is None and "result" in data:
    original_data = data["result"]
```

#### 2.5 字段映射更改
| 旧API字段 | 新API字段 | 说明 |
|-----------|-----------|------|
| `ticker` | `tick` | tick名称 |
| `inscriptionId` | `deploy_inscription_id` | 部署铭文ID |
| `max` | `max_supply` | 最大供应量 |
| `confirmedMinted` | `minted_supply` | 已铸造量 |
| `decimal` | `decimals` | 小数位数 |
| `limit` | `limit_per_mint` | 每次铸造限制 |
| `selfMint` | `is_self_mint` | 是否自铸 |
| `deployHeight` | `deploy_block_height` | 部署区块高度 |

## 📊 API响应格式对比

### 旧API响应格式 (Unisat)
```json
{
  "code": 0,
  "msg": "ok",
  "data": {
    "ticker": "Jude",
    "max": "21000000",
    "limit": "1000",
    "minted": "1655050",
    "decimal": 18,
    "selfMint": false,
    "inscriptionId": "dedbb2ff51ad066627e4a52cf73a3e2ae34354a77067805bcc24c2e393bbda56i0",
    "deployHeight": 786818,
    ...
  }
}
```

### 新API响应格式
```json
{
  "error": null,
  "result": {
    "tick": "Jude",
    "max_supply": "21000000",
    "limit_per_mint": "1000",
    "minted_supply": "1655050",
    "decimals": 18,
    "is_self_mint": false,
    "deploy_inscription_id": "dedbb2ff51ad066627e4a52cf73a3e2ae34354a77067805bcc24c2e393bbda56i0",
    "deploy_block_height": 786818,
    "remaining_supply": "19344950",
    "is_fully_minted": false,
    ...
  }
}
```

## ✅ 测试验证

### 1. API连接测试
- ✅ 新API接口连接正常
- ✅ 支持不同大小写的tick查询 (jude, Jude, JUDE)
- ✅ 正确处理不存在的tick

### 2. 数据解析测试
- ✅ 字段映射正确
- ✅ 数值转换正常 (考虑小数位数)
- ✅ 缓存机制正常工作

### 3. 兼容性测试
- ✅ 现有代码逻辑无需修改
- ✅ 数据格式保持向后兼容
- ✅ 错误处理机制正常

## 🎯 迁移优势

1. **更快的响应速度**: 新API服务器响应更快
2. **更丰富的数据**: 提供了额外的字段如 `remaining_supply`, `is_fully_minted` 等
3. **无需授权**: 简化了API调用流程
4. **更稳定的服务**: 自控API服务更可靠

## 📝 注意事项

1. 新API目前不提供某些旧字段（如 `creator` 地址），已设置为默认值
2. 保持了完整的向后兼容性，现有业务逻辑无需修改
3. 缓存机制继续有效，提高了查询效率

## 🚀 部署状态

- ✅ 配置文件已更新
- ✅ 主程序代码已修改
- ✅ 测试验证通过
- ✅ 可以正常启动和运行

迁移已完成，系统可以正常使用新的API接口！
