# -*- coding: utf-8 -*-
"""
批量查询历史区块交易脚本
基于 labiebu.py 开发，用于查询包含特定 witness 数据的历史区块交易
"""
import time
import requests
import json
import logging
import os
from typing import List
import openpyxl
from openpyxl.styles import Font, Alignment
from tqdm import tqdm

# 加载配置文件（复用 labiebu.py 的配置加载逻辑）
def load_config():
    try:
        config_path = os.path.join(os.path.dirname(__file__), 'config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 如果配置文件不存在,使用默认配置
            default_config = {
                "bitcoin_node": {
                    "rpc_user": "xiamms",
                    "rpc_password": "77534272",
                    "rpc_host": "127.0.0.1",
                    "rpc_port": "8085"
                },
                "logging": {
                    "level": "INFO"
                }
            }
            return default_config
    except Exception as e:
        logging.error(f"加载配置文件失败: {str(e)}")
        raise

# 加载配置
config = load_config()

# 连接比特币节点
rpc_user = config['bitcoin_node']['rpc_user']
rpc_password = config['bitcoin_node']['rpc_password']
rpc_host = config['bitcoin_node']['rpc_host']
rpc_port = config['bitcoin_node']['rpc_port']
rpc_url = f'http://{rpc_host}:{rpc_port}'

# 目标字符串
TARGET_STRING = "96053db5b18967b5a410326ecca687441579225a6d190f398e2180deec6e429e"

# 全局变量
current_id = 1

# 配置日志记录器
def setup_logging():
    """设置日志配置"""
    try:
        log_level_str = config.get('logging', {}).get('level', 'INFO').upper()
        log_level = getattr(logging, log_level_str, logging.INFO)
    except:
        log_level = logging.INFO
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

# 设置日志
setup_logging()

def log_info(*args):
    message = ' '.join(str(arg) for arg in args)
    logging.info(message)

def log_error(*args):
    message = ' '.join(str(arg) for arg in args)
    logging.error(message)

def log_warning(*args):
    message = ' '.join(str(arg) for arg in args)
    logging.warning(message)

def rpc_call(method, params=[]):
    """执行单个RPC调用"""
    global current_id
    query = {
        "id": current_id,
        "jsonrpc": "2.0",
        "method": method,
        "params": params
    }
    current_id += 1
    
    try:
        payload = json.dumps(query)
        response = requests.post(rpc_url, auth=(rpc_user, rpc_password), data=payload)
        response_data = response.json()
        
        if 'result' in response_data:
            return response_data['result']
        else:
            log_error(f"RPC调用失败: {response_data.get('error', '未知错误')}")
            return None
            
    except requests.exceptions.ConnectionError as e:
        log_error(f"连接错误: {e}")
        return None
    except Exception as e:
        log_error(f"RPC调用异常: {e}")
        return None

def rpc_batch(requests_list):
    """执行批量RPC调用"""
    global current_id
    
    payload = json.dumps(requests_list)
    current_id = requests_list[-1]['id'] + 1
    
    try:
        response = requests.post(rpc_url, auth=(rpc_user, rpc_password), data=payload)
        response_data = response.json()
        
        if isinstance(response_data, list):
            return response_data
        else:
            log_error(f"批量RPC调用失败: {response_data}")
            return None
            
    except requests.exceptions.ConnectionError as e:
        log_error(f"连接错误: {e}")
        return None
    except Exception as e:
        log_error(f"批量RPC调用异常: {e}")
        return None

def getblockcount():
    """获取当前区块高度"""
    return rpc_call('getblockcount')

def getblockhash(block_height):
    """获取指定区块的哈希"""
    return rpc_call('getblockhash', [block_height])

def getblock(blockhash):
    """获取区块详细信息"""
    return rpc_call('getblock', [blockhash, 2])

class TransactionRecord:
    """交易记录数据类"""
    def __init__(self, mint_address: str, receive_address: str, txid: str, block_height: int):
        self.mint_address = mint_address
        self.receive_address = receive_address
        self.txid = txid
        self.block_height = block_height

def check_witness_conditions(witness_data: str) -> bool:
    """
    检查witness数据是否符合条件：
    1. 长度大于1000个字符
    2. 包含特定字符串
    """
    if len(witness_data) <= 1000:
        return False
    
    if TARGET_STRING not in witness_data:
        return False
    
    return True

def extract_addresses_from_transaction(tx_data: dict) -> tuple:
    """
    从交易数据中提取铸造地址和收货地址
    返回: (mint_address, receive_address)
    """
    mint_address = None
    receive_address = None
    
    try:
        # 提取铸造地址：从 vin[0].prevout.scriptpubkey_address
        if tx_data.get('vin') and len(tx_data['vin']) > 0:
            vin0 = tx_data['vin'][0]
            if 'prevout' in vin0 and 'scriptpubkey_address' in vin0['prevout']:
                mint_address = vin0['prevout']['scriptpubkey_address']
        
        # 提取收货地址：从 vout[0].scriptpubkey_address  
        if tx_data.get('vout') and len(tx_data['vout']) > 0:
            vout0 = tx_data['vout'][0]
            if 'scriptPubKey' in vout0 and 'address' in vout0['scriptPubKey']:
                receive_address = vout0['scriptPubKey']['address']
                
    except Exception as e:
        log_warning(f"提取地址时出错: {e}")
    
    return mint_address, receive_address

def process_block(block_height: int) -> List[TransactionRecord]:
    """
    处理单个区块，查找符合条件的交易
    返回符合条件的交易记录列表
    """
    records = []
    
    try:
        # 获取区块哈希
        blockhash = getblockhash(block_height)
        if not blockhash:
            log_warning(f"无法获取区块 {block_height} 的哈希")
            return records
        
        # 获取区块数据
        block_data = getblock(blockhash)
        if not block_data:
            log_warning(f"无法获取区块 {block_height} 的数据")
            return records
        
        # 遍历区块中的所有交易
        transactions = block_data.get('tx', [])
        for tx in transactions:
            txid = tx.get('txid')
            if not txid:
                continue
            
            # 检查交易是否包含witness字段
            has_valid_witness = False
            for vin in tx.get('vin', []):
                witness_list = vin.get('txinwitness', [])
                for witness in witness_list:
                    if check_witness_conditions(witness):
                        has_valid_witness = True
                        break
                if has_valid_witness:
                    break
            
            # 如果找到符合条件的witness，提取地址信息
            if has_valid_witness:
                mint_address, receive_address = extract_addresses_from_transaction(tx)
                if mint_address and receive_address:
                    record = TransactionRecord(
                        mint_address=mint_address,
                        receive_address=receive_address,
                        txid=txid,
                        block_height=block_height
                    )
                    records.append(record)
                    log_info(f"找到符合条件的交易: {txid} 在区块 {block_height}")
    
    except Exception as e:
        log_error(f"处理区块 {block_height} 时出错: {e}")
    
    return records

def create_excel_file(records: List[TransactionRecord], filename: str):
    """
    创建Excel文件并写入交易记录
    """
    try:
        # 创建工作簿和工作表
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "交易记录"

        # 设置表头
        headers = ["编号", "铸造地址", "收货地址", "交易哈希", "区块编号"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # 写入数据
        for idx, record in enumerate(records, 1):
            ws.cell(row=idx+1, column=1, value=idx)
            ws.cell(row=idx+1, column=2, value=record.mint_address)
            ws.cell(row=idx+1, column=3, value=record.receive_address)
            ws.cell(row=idx+1, column=4, value=record.txid)
            ws.cell(row=idx+1, column=5, value=record.block_height)

        # 调整列宽
        column_widths = [8, 45, 45, 70, 12]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width

        # 保存文件
        wb.save(filename)
        log_info(f"Excel文件已保存: {filename}")
        log_info(f"共写入 {len(records)} 条记录")

    except Exception as e:
        log_error(f"创建Excel文件失败: {e}")
        raise

def validate_block_range(start_block: int, end_block: int) -> bool:
    """
    验证区块范围的有效性
    """
    if start_block <= 0 or end_block <= 0:
        log_error("区块编号必须大于0")
        return False

    if start_block > end_block:
        log_error("开始区块编号不能大于结束区块编号")
        return False

    # 检查区块范围是否过大
    block_range = end_block - start_block + 1
    if block_range > 10000:
        log_warning(f"区块范围较大 ({block_range} 个区块)，处理可能需要较长时间")
        response = input("是否继续？(y/n): ")
        if response.lower() != 'y':
            return False

    # 检查当前区块高度
    current_height = getblockcount()
    if current_height is None:
        log_error("无法获取当前区块高度")
        return False

    if end_block > current_height:
        log_error(f"结束区块 {end_block} 超过当前最新区块 {current_height}")
        return False

    return True

def batch_scan_blocks(start_block: int, end_block: int) -> List[TransactionRecord]:
    """
    批量扫描区块范围内的交易
    """
    all_records = []
    total_blocks = end_block - start_block + 1

    log_info(f"开始扫描区块范围: {start_block} - {end_block} (共 {total_blocks} 个区块)")

    # 使用进度条显示处理进度
    with tqdm(total=total_blocks, desc="扫描区块", unit="block") as pbar:
        for block_height in range(start_block, end_block + 1):
            try:
                records = process_block(block_height)
                all_records.extend(records)

                # 更新进度条描述
                pbar.set_postfix({
                    "当前区块": block_height,
                    "找到交易": len(records),
                    "总计": len(all_records)
                })
                pbar.update(1)

                # 添加小延迟避免过于频繁的RPC调用
                time.sleep(0.1)

            except Exception as e:
                log_error(f"扫描区块 {block_height} 时出错: {e}")
                pbar.update(1)
                continue

    log_info(f"扫描完成，共找到 {len(all_records)} 笔符合条件的交易")
    return all_records

def main():
    """主函数"""
    print("=" * 60)
    print("批量查询历史区块交易脚本")
    print("=" * 60)

    try:
        # 获取用户输入
        while True:
            try:
                start_block = int(input("请输入开始区块编号: "))
                end_block = int(input("请输入结束区块编号: "))
                break
            except ValueError:
                print("请输入有效的整数")

        # 验证区块范围
        if not validate_block_range(start_block, end_block):
            return

        # 生成输出文件名
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"block_transactions_{start_block}_{end_block}_{timestamp}.xlsx"

        print(f"\n开始处理区块范围: {start_block} - {end_block}")
        print(f"输出文件: {filename}")
        print(f"查找条件:")
        print(f"  - witness数据长度 > 1000字符")
        print(f"  - 包含特定字符串: {TARGET_STRING[:20]}...")
        print()

        # 开始扫描
        start_time = time.time()
        records = batch_scan_blocks(start_block, end_block)

        if records:
            # 创建Excel文件
            create_excel_file(records, filename)

            # 显示统计信息
            print(f"\n处理完成!")
            print(f"处理时间: {time.time() - start_time:.2f} 秒")
            print(f"找到符合条件的交易: {len(records)} 笔")
            print(f"结果已保存到: {filename}")

            # 显示前几条记录作为示例
            if len(records) > 0:
                print(f"\n前3条记录示例:")
                for i, record in enumerate(records[:3], 1):
                    print(f"  {i}. 区块:{record.block_height} 交易:{record.txid[:16]}...")
                    print(f"     铸造地址: {record.mint_address}")
                    print(f"     收货地址: {record.receive_address}")
        else:
            print(f"\n在指定区块范围内未找到符合条件的交易")

    except KeyboardInterrupt:
        print(f"\n\n用户中断操作")
    except Exception as e:
        log_error(f"程序执行出错: {e}")
        print(f"程序执行出错，请查看日志获取详细信息")

if __name__ == "__main__":
    main()
