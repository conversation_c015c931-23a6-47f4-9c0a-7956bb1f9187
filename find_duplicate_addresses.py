# -*- coding: utf-8 -*-
"""
查找重复铸造地址的脚本
读取Excel文件，找出铸造地址重复的记录，并输出到新的Excel文件
"""
import os
import sys
from typing import List, Dict
import openpyxl
from openpyxl.styles import Font, Alignment
from collections import defaultdict
import time

class TransactionRecord:
    """交易记录数据类"""
    def __init__(self, row_num: int, serial_num: int, mint_address: str, receive_address: str, txid: str, block_height: int):
        self.row_num = row_num  # 在原文件中的行号
        self.serial_num = serial_num  # 编号
        self.mint_address = mint_address  # 铸造地址
        self.receive_address = receive_address  # 收货地址
        self.txid = txid  # 交易哈希
        self.block_height = block_height  # 区块编号
    
    def __str__(self):
        return f"编号{self.serial_num}: {self.mint_address[:20]}... -> {self.receive_address[:20]}... (区块{self.block_height})"

def read_excel_file(filename: str) -> List[TransactionRecord]:
    """
    读取Excel文件并解析交易记录
    """
    records = []
    
    try:
        # 打开Excel文件
        wb = openpyxl.load_workbook(filename)
        ws = wb.active
        
        print(f"正在读取文件: {filename}")
        print(f"工作表名称: {ws.title}")
        print(f"总行数: {ws.max_row}")
        
        # 跳过表头，从第2行开始读取
        for row_num in range(2, ws.max_row + 1):
            try:
                # 读取各列数据
                serial_num = ws.cell(row=row_num, column=1).value
                mint_address = ws.cell(row=row_num, column=2).value
                receive_address = ws.cell(row=row_num, column=3).value
                txid = ws.cell(row=row_num, column=4).value
                block_height = ws.cell(row=row_num, column=5).value
                
                # 检查数据完整性
                if not all([serial_num, mint_address, receive_address, txid, block_height]):
                    print(f"警告: 第{row_num}行数据不完整，跳过")
                    continue
                
                # 创建记录对象
                record = TransactionRecord(
                    row_num=row_num,
                    serial_num=int(serial_num),
                    mint_address=str(mint_address).strip(),
                    receive_address=str(receive_address).strip(),
                    txid=str(txid).strip(),
                    block_height=int(block_height)
                )
                
                records.append(record)
                
            except Exception as e:
                print(f"警告: 处理第{row_num}行时出错: {e}")
                continue
        
        print(f"成功读取 {len(records)} 条记录")
        wb.close()
        
    except FileNotFoundError:
        print(f"错误: 文件 {filename} 不存在")
        return []
    except Exception as e:
        print(f"错误: 读取文件失败: {e}")
        return []
    
    return records

def find_duplicate_mint_addresses(records: List[TransactionRecord]) -> Dict[str, List[TransactionRecord]]:
    """
    查找重复的铸造地址
    返回: {铸造地址: [记录列表]}
    """
    address_groups = defaultdict(list)
    
    # 按铸造地址分组
    for record in records:
        address_groups[record.mint_address].append(record)
    
    # 只保留有重复的地址组
    duplicate_groups = {addr: records_list for addr, records_list in address_groups.items() if len(records_list) > 1}
    
    return duplicate_groups

def create_duplicate_excel(duplicate_records: List[TransactionRecord], output_filename: str):
    """
    创建包含重复记录的Excel文件
    """
    try:
        # 创建工作簿和工作表
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "重复铸造地址记录"
        
        # 设置表头
        headers = ["编号", "铸造地址", "收货地址", "交易哈希", "区块编号", "原文件行号", "重复组"]
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')
        
        # 按铸造地址分组并写入数据
        current_row = 2
        group_num = 1
        
        # 重新按地址分组以便输出
        address_groups = defaultdict(list)
        for record in duplicate_records:
            address_groups[record.mint_address].append(record)
        
        for mint_address, records_in_group in address_groups.items():
            # 按区块高度排序（可选）
            records_in_group.sort(key=lambda x: x.block_height)
            
            for record in records_in_group:
                ws.cell(row=current_row, column=1, value=record.serial_num)
                ws.cell(row=current_row, column=2, value=record.mint_address)
                ws.cell(row=current_row, column=3, value=record.receive_address)
                ws.cell(row=current_row, column=4, value=record.txid)
                ws.cell(row=current_row, column=5, value=record.block_height)
                ws.cell(row=current_row, column=6, value=record.row_num)
                ws.cell(row=current_row, column=7, value=f"组{group_num}")
                current_row += 1
            
            group_num += 1
        
        # 调整列宽
        column_widths = [8, 45, 45, 70, 12, 12, 10]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width
        
        # 保存文件
        wb.save(output_filename)
        print(f"重复记录已保存到: {output_filename}")
        print(f"共写入 {current_row - 2} 条重复记录")
        
    except Exception as e:
        print(f"创建Excel文件失败: {e}")
        raise

def analyze_duplicates(duplicate_groups: Dict[str, List[TransactionRecord]]):
    """
    分析重复记录的统计信息
    """
    print("\n=== 重复地址分析 ===")
    print(f"发现 {len(duplicate_groups)} 个重复的铸造地址")
    
    total_duplicate_records = 0
    max_duplicates = 0
    max_duplicate_address = ""
    
    for mint_address, records_list in duplicate_groups.items():
        count = len(records_list)
        total_duplicate_records += count
        
        if count > max_duplicates:
            max_duplicates = count
            max_duplicate_address = mint_address
        
        print(f"地址 {mint_address[:20]}... 有 {count} 条重复记录")
        for record in records_list:
            print(f"  - 编号{record.serial_num}: 区块{record.block_height}, 交易{record.txid[:16]}...")
    
    print(f"\n统计信息:")
    print(f"  重复地址总数: {len(duplicate_groups)}")
    print(f"  重复记录总数: {total_duplicate_records}")
    print(f"  最多重复次数: {max_duplicates}")
    if max_duplicate_address:
        print(f"  最多重复的地址: {max_duplicate_address[:30]}...")

def main():
    """主函数"""
    print("重复铸造地址查找工具")
    print("=" * 50)
    
    # 获取输入文件名
    if len(sys.argv) > 1:
        input_filename = sys.argv[1]
    else:
        input_filename = input("请输入Excel文件名 (或直接回车使用默认文件): ").strip()
        if not input_filename:
            # 查找当前目录下的Excel文件
            excel_files = [f for f in os.listdir('.') if f.endswith('.xlsx') and 'transactions' in f]
            if excel_files:
                input_filename = excel_files[0]  # 使用第一个找到的文件
                print(f"使用文件: {input_filename}")
            else:
                print("未找到交易Excel文件")
                return
    
    # 检查文件是否存在
    if not os.path.exists(input_filename):
        print(f"错误: 文件 {input_filename} 不存在")
        return
    
    # 读取Excel文件
    print(f"\n正在读取文件: {input_filename}")
    records = read_excel_file(input_filename)
    
    if not records:
        print("没有读取到有效记录")
        return
    
    # 查找重复的铸造地址
    print(f"\n正在查找重复的铸造地址...")
    duplicate_groups = find_duplicate_mint_addresses(records)
    
    if not duplicate_groups:
        print("✅ 没有发现重复的铸造地址！")
        return
    
    # 分析重复记录
    analyze_duplicates(duplicate_groups)
    
    # 收集所有重复记录
    all_duplicate_records = []
    for records_list in duplicate_groups.values():
        all_duplicate_records.extend(records_list)
    
    # 生成输出文件名
    base_name = os.path.splitext(input_filename)[0]
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    output_filename = f"{base_name}_duplicates_{timestamp}.xlsx"
    
    # 创建重复记录的Excel文件
    print(f"\n正在创建重复记录文件...")
    create_duplicate_excel(all_duplicate_records, output_filename)
    
    print(f"\n✅ 处理完成!")
    print(f"原文件记录数: {len(records)}")
    print(f"重复记录数: {len(all_duplicate_records)}")
    print(f"重复地址数: {len(duplicate_groups)}")
    print(f"重复记录已保存到: {output_filename}")

if __name__ == "__main__":
    main()
