import requests
import json

# 使用配置文件中的RPC设置
rpc_url = 'http://65.108.102.41:8082'
rpc_user = '__cookie__'
rpc_password = 'deb7ba3f8be7ce034259adb1e1838140b31cd316709e6de00e6f550330050ff6'

def rpc_call(method, params=[]):
    query = {
        'id': 1,
        'jsonrpc': '2.0',
        'method': method,
        'params': params
    }
    try:
        payload = json.dumps(query)
        response = requests.post(rpc_url, auth=(rpc_user, rpc_password), data=payload)
        response_data = response.json()
        return response_data.get('result')
    except Exception as e:
        print(f'RPC调用失败: {e}')
        return None

# 获取交易详细信息
txid = '86349755fd5d89bc83a969449e56233543a74dfc93287aed3ff29f3d80d7a2d7'
print(f'正在获取交易: {txid}')
tx_data = rpc_call('getrawtransaction', [txid, True])

if tx_data:
    print('\n=== 交易基本信息 ===')
    print(f'TXID: {tx_data.get("txid")}')
    print(f'区块哈希: {tx_data.get("blockhash", "未确认")}')
    print(f'确认数: {tx_data.get("confirmations", 0)}')
    
    print('\n=== VIN信息 ===')
    for i, vin in enumerate(tx_data.get('vin', [])):
        print(f'VIN[{i}]:')
        print(f'  txid: {vin.get("txid", "coinbase")}')
        print(f'  vout: {vin.get("vout", "N/A")}')
        
        # 检查witness数据
        witness = vin.get('txinwitness', [])
        if witness:
            print(f'  witness数量: {len(witness)}')
            for j, w in enumerate(witness):
                print(f'    witness[{j}] 长度: {len(w)} 字符')
                if len(w) > 100:
                    print(f'    witness[{j}] 前100字符: {w[:100]}...')
                else:
                    print(f'    witness[{j}]: {w}')
        
        # 检查prevout信息（如果有）
        if 'prevout' in vin:
            prevout = vin['prevout']
            print(f'  prevout scriptpubkey_address: {prevout.get("scriptpubkey_address", "无")}')
    
    print('\n=== VOUT信息 ===')
    for i, vout in enumerate(tx_data.get('vout', [])):
        print(f'VOUT[{i}]:')
        print(f'  value: {vout.get("value")} BTC')
        script_pubkey = vout.get('scriptPubKey', {})
        print(f'  address: {script_pubkey.get("address", "无地址")}')
        print(f'  type: {script_pubkey.get("type")}')
    # 如果有vin信息，尝试获取输入地址
    if tx_data.get('vin') and len(tx_data['vin']) > 0:
        vin0 = tx_data['vin'][0]
        if 'txid' in vin0 and 'vout' in vin0:
            prev_txid = vin0['txid']
            prev_vout = vin0['vout']
            print(f'\n=== 查询输入地址 ===')
            print(f'前一个交易: {prev_txid}')
            print(f'输出索引: {prev_vout}')

            # 查询前一个交易
            prev_tx = rpc_call('getrawtransaction', [prev_txid, True])
            if prev_tx and 'vout' in prev_tx:
                if prev_vout < len(prev_tx['vout']):
                    prev_vout_data = prev_tx['vout'][prev_vout]
                    if 'scriptPubKey' in prev_vout_data and 'address' in prev_vout_data['scriptPubKey']:
                        input_address = prev_vout_data['scriptPubKey']['address']
                        print(f'铸造地址: {input_address}')
                    else:
                        print('前一个交易输出中没有地址信息')
                else:
                    print(f'前一个交易的vout索引 {prev_vout} 超出范围')
            else:
                print('无法获取前一个交易数据')

else:
    print('无法获取交易数据')
