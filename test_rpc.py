import requests
import json

# 使用配置文件中的RPC设置
rpc_url = 'http://127.0.0.1:8085'
rpc_user = 'xiamms'
rpc_password = '77534272'

def rpc_call(method, params=[]):
    query = {
        'id': 1,
        'jsonrpc': '2.0',
        'method': method,
        'params': params
    }
    try:
        payload = json.dumps(query)
        response = requests.post(rpc_url, auth=(rpc_user, rpc_password), data=payload)
        response_data = response.json()
        return response_data.get('result')
    except Exception as e:
        print(f'RPC调用失败: {e}')
        return None

# 获取交易详细信息
txid = '86349755fd5d89bc83a969449e56233543a74dfc93287aed3ff29f3d80d7a2d7'
print(f'正在获取交易: {txid}')
tx_data = rpc_call('getrawtransaction', [txid, True])

if tx_data:
    print('\n=== 交易基本信息 ===')
    print(f'TXID: {tx_data.get("txid")}')
    print(f'区块哈希: {tx_data.get("blockhash", "未确认")}')
    print(f'确认数: {tx_data.get("confirmations", 0)}')
    
    print('\n=== VIN信息 ===')
    for i, vin in enumerate(tx_data.get('vin', [])):
        print(f'VIN[{i}]:')
        print(f'  txid: {vin.get("txid", "coinbase")}')
        print(f'  vout: {vin.get("vout", "N/A")}')
        
        # 检查witness数据
        witness = vin.get('txinwitness', [])
        if witness:
            print(f'  witness数量: {len(witness)}')
            for j, w in enumerate(witness):
                print(f'    witness[{j}] 长度: {len(w)} 字符')
                if len(w) > 100:
                    print(f'    witness[{j}] 前100字符: {w[:100]}...')
                else:
                    print(f'    witness[{j}]: {w}')
        
        # 检查prevout信息（如果有）
        if 'prevout' in vin:
            prevout = vin['prevout']
            print(f'  prevout scriptpubkey_address: {prevout.get("scriptpubkey_address", "无")}')
    
    print('\n=== VOUT信息 ===')
    for i, vout in enumerate(tx_data.get('vout', [])):
        print(f'VOUT[{i}]:')
        print(f'  value: {vout.get("value")} BTC')
        script_pubkey = vout.get('scriptPubKey', {})
        print(f'  address: {script_pubkey.get("address", "无地址")}')
        print(f'  type: {script_pubkey.get("type")}')
else:
    print('无法获取交易数据')
