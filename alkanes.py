import binascii
import sys
import math
from typing import List, Optional, Tuple, Dict
from dataclasses import dataclass, field

# ------------ 常量定义 (合并自 alkanes_parser.py) ------------

# Runes常量
MAX_DIVISIBILITY = 38
MAX_SPACERS = 0b00000111_11111111_11111111_11111111

# Runes标签定义 (更完整)
class RuneTag:
    """Runes协议中的标签定义"""
    Body = 0         # Edicts (不是 Mint 信息)
    Divisibility = 1 # 可分性（小数位数）
    Flags = 2        # 标志位
    Spacers = 3      # 间隔符
    Rune = 4         # 符文名称 (整数表示)
    Symbol = 5       # 符号名称 (单个字符的整数表示)
    Premine = 6      # 预挖数量
    Cap = 8          # 发行上限 (针对 Mint 条款)
    Amount = 10      # 单次 Mint 数量 (针对 Mint 条款)
    HeightStart = 12 # Mint 开始高度
    HeightEnd = 14   # Mint 结束高度
    OffsetStart = 16 # Mint 开始偏移
    OffsetEnd = 18   # Mint 结束偏移
    Mint = 20        # Mint 的 Rune ID 信息 (Block, Tx)
    Pointer = 22     # Runes 指针 (输出索引)
    Cenotaph = 126   # 纪念碑 (无效 Runestone)
    Nop = 127        # 空操作 (忽略)
    Protocol = 16383 # Alkanes/Protorunes 专用的 Protocol 字段

# Runes标志位定义
class RuneFlag:
    """Runes协议中的标志位定义"""
    Etching = 0      # 部署新 Rune
    Terms = 1        # 部署包含 Mint 条款
    Turbo = 2        # 可升级性 (目前未使用)
    Cenotaph = 127   # 标记为无效 Runestone

# Alkanes/Protorunes 协议标签定义 (确保定义完整且无误)
class ProtoTag:
    """Alkanes/Protorunes协议中的标签定义"""
    Body = 0         # 主体数据，包含 Edicts
    Message = 81     # 消息/calldata (Alkanes 下可能编码特定调用)
    Burn = 83        # 燃烧数量
    Pointer = 91     # 输出指针 (指向 Output 或其他 Protostone)
    Refund = 93      # 退款指针 (指向 Output 或其他 Protostone)
    From = 95        # 来源 Edict 索引列表 (用于 Protoburn)
    Cenotaph = 126   # 纪念碑 (无效 Protostone)
    Nop = 127        # 空操作

# Alkanes Token Standard Opcodes (根据文档添加)
class AlkaneOpcode:
    """Alkanes Token Standard 定义的操作码"""
    INITIALIZE = 0
    MINT = 77        # Mint 或 Transfer (根据文档描述，77 可能两者都用)
    NAME = 99
    SYMBOL = 100
    TOTAL_SUPPLY = 101
    CAP = 102
    MINTED = 103
    VALUE_PER_MINT = 104
    METADATA = 1000
    DATA = 999       # 获取所有数据

    # 辅助函数，用于获取操作码名称
    @classmethod
    def get_name(cls, opcode):
        for name, value in vars(cls).items():
            if value == opcode:
                return name
        return "UNKNOWN_OPCODE"

# Create sets of valid tag values for quick lookup
RUNE_TAG_VALUES = {
    RuneTag.Body, RuneTag.Divisibility, RuneTag.Flags, RuneTag.Spacers,
    RuneTag.Rune, RuneTag.Symbol, RuneTag.Premine, RuneTag.Cap,
    RuneTag.Amount, RuneTag.HeightStart, RuneTag.HeightEnd,
    RuneTag.OffsetStart, RuneTag.OffsetEnd, RuneTag.Mint,
    RuneTag.Pointer, RuneTag.Cenotaph, RuneTag.Nop, RuneTag.Protocol
}

PROTO_TAG_VALUES = {
    ProtoTag.Body, ProtoTag.Message, ProtoTag.Burn, ProtoTag.Pointer,
    ProtoTag.Refund, ProtoTag.From, ProtoTag.Cenotaph, ProtoTag.Nop
}

# ------------ 基础数据结构 (合并) ------------

@dataclass
class RuneId:
    """符文ID类，用于保存区块和交易标识符"""
    block: int
    tx: int

    def __str__(self):
        """返回符文ID的字符串表示"""
        return f"{self.block}:{self.tx}"

@dataclass
class Edict:
    """Edict类，表示一个转移指令 (统一结构)"""
    rune_id: RuneId # 使用 RuneId 对象
    amount: int
    output: int

    def __str__(self):
        return f"Edict(RuneID={self.rune_id}, 金额={self.amount}, 输出={self.output})"

@dataclass
class ProtoStone:
    protocol_id: int
    # Make sure fields match what parse_protocol_fields populates
    edicts: List[Edict] = field(default_factory=list) # Protostone 内部的 Edict 列表
    pointer: Optional[int] = None
    refund_pointer: Optional[int] = None
    burn: Optional[int] = None
    message: Optional[bytes] = None # 通用消息存储字节
    from_indices: List[int] = field(default_factory=list)
    alkane_call: Optional[dict] = None #特定于 Alkanes (ID=1)
    residual_tags: Dict[int, List[int]] = field(default_factory=dict)

    def __str__(self):
        """返回Protostone的字符串表示"""
        result = [f"Protostone(Protocol ID: {self.protocol_id})"]
        if self.edicts:
            result.append(f"  Edicts: {len(self.edicts)}")
            for edict in self.edicts[:3]: # 只打印前3个 Edict
                result.append(f"    - {edict}")
            if len(self.edicts) > 3:
                result.append(f"    ... and {len(self.edicts) - 3} more")
        if self.pointer is not None: result.append(f"  Pointer: {self.pointer}")
        if self.refund_pointer is not None: result.append(f"  Refund Pointer: {self.refund_pointer}")
        if self.burn is not None: result.append(f"  Burn: {self.burn}")
        if self.message is not None: result.append(f"  Message (通用) (hex): {self.message.hex()}")
        if self.from_indices: result.append(f"  From Indices: {self.from_indices}")
        if self.alkane_call is not None:
            opcode_name = AlkaneOpcode.get_name(self.alkane_call.get('opcode'))
            alkane_id_params = self.alkane_call.get('alkaneId')
            result.append(f"  Alkanes Call:")
            result.append(f"    Opcode: {self.alkane_call.get('opcode')} ({opcode_name})")
            result.append(f"    AlkaneID: {alkane_id_params}")
        if self.residual_tags:
             result.append(f"  Residual Tags: {self.residual_tags}")

        return "\n".join(result)

# Define RunestoneContainer *before* it's used in type hints
@dataclass
class EtchingData:
    # Define fields based on parse_etching output
    divisibility: int = 0
    premine: str = "0"
    rune_int: Optional[int] = None
    rune_name: Optional[str] = None
    spacers: int = 0
    symbol: Optional[str] = None
    terms: Optional[dict] = None # Contains cap, amount, height, offset
    turbo: bool = False
    name_with_spacers: str = ""
    supply: Optional[int] = None # Supply as integer

@dataclass
class RunestoneContainer:
    mint: Optional[RuneId] = None
    pointer: Optional[int] = None
    edicts: List[Edict] = field(default_factory=list) # Top-level Runes edicts
    etching: Optional[EtchingData] = None # Use the EtchingData class
    protostones: List[ProtoStone] = field(default_factory=list)
    cenotaph: bool = False
    residual_data: List[int] = field(default_factory=list)

    def __str__(self): # Add a __str__ for easier printing
        parts = []
        parts.append(f"RunestoneContainer(Cenotaph: {self.cenotaph}")
        if self.etching: parts.append(f"  Etching: {self.etching.name_with_spacers if self.etching.rune_name else 'Present'}")
        else: parts.append("  Etching: None")
        if self.mint: parts.append(f"  Mint ID: {self.mint}")
        else: parts.append("  Mint ID: None")
        if self.pointer is not None: parts.append(f"  Pointer: {self.pointer}")
        else: parts.append("  Pointer: None")
        parts.append(f"  Runes Edicts: {len(self.edicts)}")
        parts.append(f"  Protostones: {len(self.protostones)}")
        if self.residual_data: parts.append(f"  Residual Data: {self.residual_data}")
        parts.append(")")
        return "\n".join(parts)

# ------------ 编解码与辅助函数 (合并) ------------

def decode_leb128(data):
    """Decode LEB128 integers from bytes."""
    result = []
    value = 0
    shift = 0
    continuation_bit = 0x80
    integer_data = 0x7f
    
    for i in range(len(data)):
        byte = data[i]
        value |= ((byte & integer_data) << shift)
        if byte & continuation_bit == 0:
            result.append(value)
            value = 0
            shift = 0
        else:
            shift += 7
            if shift > 128: # 防止超长解码 (u128 最多需要约 19 字节)
                 print(f"【警告】LEB128 解码shift过大: {shift}")
                 # 可以选择抛出异常或截断
    
    # 只有当数据非空时才打印解码信息
    # if data:
    #     print(f"【LEB128解码】输入: {data.hex()}, 输出: {result}")
    return result

def format_amount(amount, divisibility=0):
    """根据 divisibility 格式化 amount 值 (添加 divisibility 参数)"""
    if divisibility > MAX_DIVISIBILITY:
        divisibility = MAX_DIVISIBILITY # 限制最大精度

    if amount == 0:
        return "0"
        
    if divisibility == 0:
        return str(amount)

    divisor = 10 ** divisibility
    integer_part = amount // divisor
    fractional_part = amount % divisor
    
    if fractional_part == 0:
        return str(integer_part)
    else:
        # 格式化小数部分，确保有 divisibility 位小数，并去除末尾的0
        fractional_str = f"{fractional_part:0{divisibility}d}".rstrip('0')
        return f"{integer_part}.{fractional_str}"

def rune_to_string(value):
    """将符文值(整数)转换为名称字符串 (来自 alkanes_parser.py)"""
    if value is None: return None
    if not isinstance(value, int) or value < 0: return "INVALID_RUNE_VALUE"
    # Based on ord reference implementation
    value += 1
    result = ""
    while value > 0:
        result = chr(ord('A') + (value - 1) % 26) + result
        value = (value - 1) // 26
    return result

def insert_spacers(name, spacers):
    """根据间隔掩码在名称中插入间隔符 (来自 alkanes_parser.py)"""
    if name is None or spacers is None: return name or ""
    spacers_bin = bin(spacers & MAX_SPACERS)[2:]
    spacers_bin = '0' * (len(name) - 1 - len(spacers_bin)) + spacers_bin # Pad spacers

    spaced_name = ""
    for i, char in enumerate(name):
        spaced_name += char
        if i < len(name) - 1 and i < len(spacers_bin) and spacers_bin[len(spacers_bin)-1-i] == '1':
            spaced_name += '•'
    return spaced_name

def check_and_clear_flag(flags, flag):
    """检查并清除标志位 (来自 alkanes_parser.py)"""
    if flags is None: flags = 0
    mask = 1 << flag
    is_set = (flags & mask) != 0
    flags &= ~mask # Clear the flag
    return is_set, flags

def parse_by_tag(data, tag_class=RuneTag):
    """根据标签解析数据 (来自 alkanes_parser.py, 稍作修改)"""
    fields = {}
    i = 0
    while i < len(data):
        tag = data[i]
        
        # Body 标签 (0) 特殊处理，其后所有数据都是它的值
        if tag == tag_class.Body:
            value_list = data[i + 1:]
            if tag in fields:
                 # 如果 Body 标签重复出现？这不符合规范，但做个保护
                 if isinstance(fields[tag], list):
                     fields[tag].extend(value_list)
                 else: # 之前存的不是列表，创建一个新列表
                     fields[tag] = [fields[tag]] + value_list
            else:
                 fields[tag] = value_list
            # print(f"  -> 解析到 Tag {tag} (Body), 值(剩余全部): {value_list}")
            break # Body 标签是最后一个
        # 其他标签，假设后面跟一个值
        elif i + 1 < len(data):
            value = data[i + 1]
            if tag in fields:
                # 如果标签已存在，将其值变成列表
                if not isinstance(fields[tag], list):
                    fields[tag] = [fields[tag]]
                fields[tag].append(value)
                # print(f"  -> 解析到重复 Tag {tag}, 追加值: {value}")
            else:
                fields[tag] = value # 第一次出现，直接赋值
                # print(f"  -> 解析到 Tag {tag}, 值: {value}")
            i += 2 # 跳过标签和值
        else:
            # 末尾的单个标签，没有值
            if tag not in fields: # 只有在第一次出现时记录
                 fields[tag] = None
            # print(f"  -> 解析到末尾 Tag {tag}, 无值")
            i += 1
            
    return fields

def parse_runes_delta_encoded_edicts(data):
    """解析 Runestone Body 中的 delta-encoded Edicts (来自 alkanes_parser.py)"""
    # print(f"【解析Runes Edict】输入整数: {data}")
    edicts = []
    index = 0
    base_block = 0
    base_tx = 0
    
    while index + 3 < len(data): # 每个 Edict 需要 4 个整数
        try:
            block_delta = int(data[index])
            tx_val = int(data[index + 1])
            amount = int(data[index + 2])
            output = int(data[index + 3])
        except (ValueError, TypeError, IndexError) as e:
            print(f"【错误】解析Runes Edict整数时出错: {e} at index {index}")
            break
            
        if block_delta > 0:
            new_block = base_block + block_delta
            new_tx = tx_val # 绝对 tx 索引
        else:
            new_block = base_block
            new_tx = base_tx + tx_val # tx 增量
            
        base_block = new_block
        base_tx = new_tx
        
        rune_id = RuneId(new_block, new_tx)
        edict = Edict(rune_id, amount, output)
        edicts.append(edict)
        # print(f"  -> 解析得到Runes Edict: {edict}")
        index += 4
        
    #if index < len(data):
        #print(f"【警告】解析Runes Edict后有多余数据: {data[index:]}")
        
    return edicts

def parse_protocol_edict(data):
    """解析 Protostone Body 中的 delta-encoded Edicts (保持不变)"""
    # print(f"【解析协议Edict】输入整数: {data}")
    edicts = []
    index = 0
    base_block = 0
    base_tx = 0
    
    while index + 3 < len(data):
        try:
            block_delta = int(data[index])
            tx_val = int(data[index + 1])
            amount = int(data[index + 2])
            output = int(data[index + 3])
        except (ValueError, TypeError, IndexError) as e:
            print(f"【错误】解析协议Edict整数时出错: {e} at index {index}")
            break
            
        if block_delta > 0:
            new_block = base_block + block_delta
            new_tx = tx_val
        else:
            new_block = base_block
            new_tx = base_tx + tx_val
            
        base_block = new_block
        base_tx = new_tx
        
        # 注意：这里创建的 RuneId 可能用于子协议内部资产
        rune_id = RuneId(new_block, new_tx)
        edict = Edict(rune_id, amount, output)
        edicts.append(edict)
        # print(f"  -> 解析得到协议Edict: {edict}")
        index += 4
        
    if index < len(data):
        print(f"【警告】解析协议Edict后有多余数据: {data[index:]}")
        
    return edicts

def parse_terms(fields):
    """解析 Etching 中的 Terms 结构 (来自 alkanes_parser.py, 稍作修改)"""
    # 注意: Terms 的 amount 需要 divisibility, 但此时可能还未知
    # 暂时存储原始 amount 值，在获取 divisibility 后再格式化
    # print("【解析Terms】...")
    terms = {}
    terms['cap'] = fields.get(RuneTag.Cap) # Cap 是单个值
    terms['amount_raw'] = fields.get(RuneTag.Amount) # Amount 是单个值, 暂存原始值
    
    height_start = fields.get(RuneTag.HeightStart)
    height_end = fields.get(RuneTag.HeightEnd)
    terms['height'] = (height_start, height_end)
    
    offset_start = fields.get(RuneTag.OffsetStart)
    offset_end = fields.get(RuneTag.OffsetEnd)
    terms['offset'] = (offset_start, offset_end)
    # print(f"  -> 解析结果 (Amount未格式化): {terms}")
    return terms

def parse_mint(fields):
    """解析 Mint 的 Rune ID (来自 alkanes_parser.py)"""
    # print("【解析Mint ID】...")
    mint_value = fields.get(RuneTag.Mint)
    # Mint tag 的值应该是 [block, tx]
    if isinstance(mint_value, list) and len(mint_value) >= 2:
        try:
             block, tx = int(mint_value[0]), int(mint_value[1])
             rune_id = RuneId(block, tx)
             # print(f"  -> 解析得到 Mint Rune ID: {rune_id}")
             return rune_id
        except (TypeError, ValueError):
             print(f"  -> 错误: Mint 标签的值无效: {mint_value}")
             return None
    elif mint_value is not None: # 如果不是列表或长度不够
         print(f"  -> 错误: Mint 标签的值格式错误: {mint_value}")
         return None
    else:
         # print("  -> 未找到 Mint 标签")
         return None # 未找到或格式错误

def parse_etching(fields):
    """解析 Etching 数据 (来自 alkanes_parser.py, 整合)"""
    # print("【解析Etching】...")
    etching = {}
    flags_raw = fields.get(RuneTag.Flags)
    # Flags 标签的值是单个整数
    flags = flags_raw if isinstance(flags_raw, int) else 0

    is_etching, flags = check_and_clear_flag(flags, RuneFlag.Etching)
    # print(f"  -> Etching标志位: {is_etching}")
    if not is_etching:
        # print("  -> 非Etching，跳过")
        return None # 不是Etching，直接返回None

    # 获取 Divisibility (单个值)
    divisibility = fields.get(RuneTag.Divisibility)
    etching['divisibility'] = divisibility if isinstance(divisibility, int) else 0
    # print(f"  -> Divisibility: {etching['divisibility']}")

    # 获取 Premine (单个值)
    premine_raw = fields.get(RuneTag.Premine)
    premine_raw = premine_raw if isinstance(premine_raw, int) else 0
    etching['premine'] = format_amount(premine_raw, etching['divisibility'])
    # print(f"  -> Premine (格式化后): {etching['premine']}")

    # 获取 Rune 名称 (整数表示, 单个值)
    rune_value = fields.get(RuneTag.Rune)
    etching['rune_int'] = rune_value if isinstance(rune_value, int) else None
    etching['rune_name'] = rune_to_string(etching['rune_int'])
    # print(f"  -> Rune (整数): {etching['rune_int']}, 名称: {etching['rune_name']}")

    # 获取 Spacers (单个值)
    spacers = fields.get(RuneTag.Spacers)
    etching['spacers'] = spacers if isinstance(spacers, int) else 0
    # print(f"  -> Spacers: {etching['spacers']}")

    # 获取 Symbol (单个字符的整数表示, 单个值)
    symbol_int = fields.get(RuneTag.Symbol)
    symbol_int = symbol_int if isinstance(symbol_int, int) else 0
    try:
         etching['symbol'] = chr(symbol_int) if 0 < symbol_int < 0x110000 else None
    except ValueError:
         etching['symbol'] = None # 无效 unicode
    # print(f"  -> Symbol (整数): {symbol_int}, 字符: {etching['symbol']}")

    # 解析 Terms (如果标志位存在)
    has_terms, flags = check_and_clear_flag(flags, RuneFlag.Terms)
    # print(f"  -> Terms标志位: {has_terms}")
    if has_terms:
        terms_data = parse_terms(fields)
        # 格式化 Terms 中的 Amount
        if terms_data.get('amount_raw') is not None:
             terms_data['amount'] = format_amount(terms_data['amount_raw'], etching['divisibility'])
        else:
             terms_data['amount'] = "0"
        # del terms_data['amount_raw'] # 移除原始值
        etching['terms'] = terms_data
    else:
        etching['terms'] = None

    # 检查 Turbo 标志位
    etching['turbo'], flags = check_and_clear_flag(flags, RuneFlag.Turbo)
    # print(f"  -> Turbo标志位: {etching['turbo']}")
    
    # 组合带间隔符的名称
    etching['name_with_spacers'] = insert_spacers(etching['rune_name'], etching['spacers'])
    # print(f"  -> 带间隔符名称: {etching['name_with_spacers']}")
    
    # 计算 Supply (如果 Cap 和 Amount 存在)
    etching['supply'] = None # 默认值
    if etching['terms'] and etching['terms'].get('cap') is not None and etching['terms'].get('amount_raw') is not None:
        try:
            premine_val = float(etching['premine']) if etching['premine'] else 0
            cap_val = int(etching['terms']['cap'])
            amount_val = float(format_amount(etching['terms']['amount_raw'], etching['divisibility'])) # 格式化后再转float
            supply = premine_val + cap_val * amount_val
            # 将 supply 转换回 rune 的整数表示 (乘以 10**divisibility)
            supply_int = int(supply * (10**etching['divisibility']))
            etching['supply'] = supply_int # 存储整数形式
            # print(f"  -> 计算得到 Supply (整数): {etching['supply']}")
        except (ValueError, TypeError) as e:
            print(f"  -> 计算 Supply 时出错: {e}")
            
    if flags != 0:
        print(f"【警告】Etching 解析后仍有未处理的 Flags: {bin(flags)}")

    return etching

# ------------ 核心解析函数 (重构与合并) ------------

def parse_protocol_fields(payload: List[int], protocol_id: int) -> Tuple[Optional[ProtoStone], List[int]]:
    """Parses a single protostone segment from a list of integers.
    Args:
        payload: The list of integers representing the fields of *this* protostone.
        protocol_id: The protocol ID for this protostone.
    Returns:
        A tuple containing:
        - The parsed ProtoStone object, or None if parsing failed within the segment.
        - An empty list, as this function should consume the entire payload.
          (The outer loop handles advancing the main index based on declared length).
    """
    # print(f"    Parsing protostone segment (ID {protocol_id}): {payload}")
    protostone = ProtoStone(protocol_id=protocol_id)
    i = 0
    residual_tags = {} # Collect unhandled tags within this protostone

    while i < len(payload):
        tag = payload[i]

        if tag == ProtoTag.Body:
            # print(f"      Body tag found at index {i}")
            edict_data = payload[i + 1:]
            protostone.edicts = parse_protocol_edict(edict_data)
            # print(f"      Parsed {len(protostone.edicts)} edicts.")
            break # Body is the last field in a protostone payload

        elif i + 1 < len(payload):
            value = payload[i + 1]
            # print(f"      Tag: {tag}, Value: {value} at index {i}")
            # --- Handle Known Tags --- 
            if tag == ProtoTag.Pointer:
                protostone.pointer = value
            elif tag == ProtoTag.Refund:
                protostone.refund_pointer = value
            elif tag == ProtoTag.Burn:
                 protostone.burn = value
            elif tag == ProtoTag.Message:
                 if protocol_id == 1: # Alkanes Specific Message Handling
                     try:
                         if not isinstance(value, int): raise TypeError("Expected int")
                         msg_byte_len = max(1, (value.bit_length() + 7) // 8)
                         msg_bytes = value.to_bytes(msg_byte_len, 'little', signed=False)
                         alkane_call_data = decode_leb128(msg_bytes)
                         if len(alkane_call_data) == 3:
                             block, tx, opcode = alkane_call_data
                             protostone.alkane_call = {
                                 'opcode': opcode,
                                 'alkaneId': {'block': block, 'tx': tx}
                             }
                         else:
                              #print(f"【警告】Protostone (ID {protocol_id}): Alkanes Message 解码格式错误, ints={alkane_call_data}, from value={value}")
                              protostone.alkane_call = None # Or store raw data?
                     except Exception as e:
                         print(f"【错误】Protostone (ID {protocol_id}): 处理 Alkanes Message 出错, value={value}, error={e}")
                         protostone.alkane_call = None
                 else: # Generic Message Handling
                     try:
                         if not isinstance(value, int): raise TypeError("Expected int")
                         msg_byte_len = max(1, (value.bit_length() + 7) // 8)
                         protostone.message = value.to_bytes(msg_byte_len, 'little', signed=False)
                     except Exception as e:
                         print(f"【错误】Protostone (ID {protocol_id}): 转换通用 Message 字节出错, value={value}, error={e}")
                         protostone.message = None
            elif tag == ProtoTag.From:
                 if not isinstance(protostone.from_indices, list): protostone.from_indices = []
                 protostone.from_indices.append(value)
            else:
                # Store unknown tags within the protostone's context
                if tag not in residual_tags:
                     residual_tags[tag] = []
                residual_tags[tag].append(value)
                print(f"【警告】Protostone (ID {protocol_id}): 未知或未处理的内部标签 {tag} (值: {value})，已记录到 residual_tags。")

            i += 2 # Move past tag and value
        else:
            # Tag without a value at the end of the payload
            print(f"【警告】Protostone (ID {protocol_id}): 在结尾处遇到无值的标签 {tag}。已记录到 residual_tags。")
            if tag not in residual_tags:
                residual_tags[tag] = []
            residual_tags[tag].append(None) # Indicate tag was present but had no value
            i += 1

    protostone.residual_tags = residual_tags
    # This function, by design, processes the entire payload segment given to it.
    # It doesn't calculate the 'next index' in the *overall* list.
    # Return the parsed object (or None if fundamentally failed) and an empty list
    # to signify the payload segment was consumed.
    # print(f"    Finished parsing segment for Protostone ID {protocol_id}. Result: {protostone}")
    return protostone, []

def parse_runestones(integers: List[int]) -> Optional[RunestoneContainer]:
    """解析 Runestone 数据流 (整数列表) 并提取信息, 包括 Protostones."""
    #print(f"\n--- Parsing Runestone from integers (length {len(integers)}) ---")
    #print(f"Input integers: {integers}")

    # 初始化 Runestone 结构
    runestone = RunestoneContainer(
        mint=None,
        pointer=None,
        edicts=[],
        etching=None,
        protostones=[],
        cenotaph=False,
        residual_data=[]
    )
    fields = {}
    i = 0

    js_unpacked_protocol_payloads = [] # Store potential JS-style protocol payloads

    while i < len(integers):
        tag = integers[i]

        if tag == RuneTag.Body:
            # Edicts are processed later from the 'fields' dict if present
            if RuneTag.Body not in fields:
                fields[RuneTag.Body] = []
            # Collect all subsequent integers until the next tag (or end)
            edict_payload = []
            j = i + 1
            while j < len(integers) and integers[j] not in RUNE_TAG_VALUES and integers[j] not in PROTO_TAG_VALUES:
                 edict_payload.append(integers[j])
                 j += 1
            fields[RuneTag.Body].extend(edict_payload)
            #print(f"Collected BODY payload: {edict_payload}")
            i = j # Move index past the edict payload
            continue # Skip standard tag processing for BODY payload

        elif tag == RuneTag.Flags:
            if i + 1 < len(integers):
                if RuneTag.Flags not in fields: fields[RuneTag.Flags] = []
                fields[RuneTag.Flags].append(integers[i + 1])
                #print(f"Found FLAGS: {integers[i+1]}")
                i += 2
            else:
                #print(f"Warning: Found FLAGS tag ({tag}) without value.")
                runestone.cenotaph = True # Mark as cenotaph
                i += 1
            continue

        elif tag == RuneTag.Mint:
             if i + 1 < len(integers):
                 if RuneTag.Mint not in fields: fields[RuneTag.Mint] = []
                 fields[RuneTag.Mint].append(integers[i+1])
                 #print(f"Found Mint: {integers[i+1]}")
                 i += 2
             else:
                 #print(f"Warning: Found Mint tag ({tag}) without value.")
                 runestone.cenotaph = True
                 i += 1
             continue

        elif tag == RuneTag.Pointer:
             if i + 1 < len(integers):
                 if RuneTag.Pointer not in fields: fields[RuneTag.Pointer] = []
                 fields[RuneTag.Pointer].append(integers[i+1])
                 #print(f"Found Runestone Pointer: {integers[i+1]}")
                 i += 2
             else:
                 #print(f"Warning: Found Runestone Pointer tag ({tag}) without value.")
                 runestone.cenotaph = True
                 i += 1
             continue

        elif tag == RuneTag.Divisibility or tag == RuneTag.Spacers or tag == RuneTag.Symbol or \
             tag == RuneTag.Premine or tag == RuneTag.Cap or tag == RuneTag.Amount or \
             tag == RuneTag.HeightStart or tag == RuneTag.HeightEnd or \
             tag == RuneTag.OffsetStart or tag == RuneTag.OffsetEnd or \
             tag == RuneTag.Rune:
             # Standard Runestone tags expecting one value
             if i + 1 < len(integers):
                 if tag not in fields: fields[tag] = []
                 fields[tag].append(integers[i+1])
                 #print(f"Found RuneTag {tag}: {integers[i+1]}")
                 i += 2
             else:
                 #print(f"Warning: Found RuneTag {tag} without value.")
                 runestone.cenotaph = True
                 i += 1
             continue

        elif tag == RuneTag.Protocol:
            # Collect integer following PROTOCOL tag, assuming JS SDK style encoding
            if i + 1 < len(integers):
                protocol_payload_value = integers[i+1]
                js_unpacked_protocol_payloads.append(protocol_payload_value)
                #print(f"Collected potential JS-style PROTOCOL payload value: {protocol_payload_value}")
                i += 2 # Consume tag and value
            else:
                # Error: PROTOCOL tag without a value
                #print(f"Warning: Found PROTOCOL tag ({tag}) at index {i} without subsequent value. Marking as Cenotaph.")
                runestone.cenotaph = True # Definitely problematic
                i += 1
            continue # Move to next tag

        elif tag == ProtoTag.Cenotaph:
             #print("Found Cenotaph tag. Marking runestone as Cenotaph.")
             runestone.cenotaph = True
             # Let's be safe and assume it *might* take a value to skip it.
             if i + 1 < len(integers):
                 # Check if next value looks like another tag using the sets
                 next_val = int(integers[i+1])
                 if next_val not in RUNE_TAG_VALUES and next_val not in PROTO_TAG_VALUES:
                      #print(f"Skipping value after CENOTAPH: {next_val}")
                      i += 2
                 else:
                      #print(f"Assuming CENOTAPH tag has no value (next item {next_val} is a known tag). Skipping tag.")
                      i += 1 # Assume no value consumed
             else:
                 i += 1 # No value following anyway
             continue


        elif tag == ProtoTag.Nop:
            #print(f"Found NOP tag ({tag}). Skipping.")
            # NOP typically doesn't consume a value, just skip the tag itself
            i += 1
            continue

        else:
            # Unknown or unhandled tag
            #print(f"Warning: Encountered unknown or unhandled tag {tag} at index {i}.")
             # Try to gracefully skip tag and potential value
            if i + 1 < len(integers):
                 # Check if next item looks like a tag using the predefined sets
                 next_val = int(integers[i+1])
                 if next_val not in RUNE_TAG_VALUES and next_val not in PROTO_TAG_VALUES:
                     #print(f"Assuming value {next_val} follows unknown tag {tag}. Skipping both.")
                     runestone.residual_data.append(tag)
                     runestone.residual_data.append(next_val)
                     i += 2
                 else:
                     #print(f"Assuming unknown tag {tag} has no value (next item {next_val} is a known tag). Skipping tag.")
                     runestone.residual_data.append(tag)
                     i += 1
            else:
                 #print(f"Assuming unknown tag {tag} at end has no value. Skipping tag.")
                 runestone.residual_data.append(tag)
                 i += 1
            continue # Continue parsing remaining integers

    # --- After the main tag parsing loop ---
    #print("\n--- Finished parsing Runestone tags ---")
    #print(f"Collected fields: {fields}")
    #print(f"Collected potential JS-style PROTOCOL payloads: {js_unpacked_protocol_payloads}")
    #print(f"Residual data (unhandled tags/values): {runestone.residual_data}")
    #print(f"Is Cenotaph: {runestone.cenotaph}")

    # Process Runestone fields (Etching, Mint, Pointer) if not Cenotaph
    if not runestone.cenotaph:
        # Parse Etching details - Use correct case for Flags
        if RuneTag.Flags in fields:
             flags_value = fields[RuneTag.Flags][0]
             is_etching, _ = check_and_clear_flag(flags_value, RuneFlag.Etching)
             if is_etching:
                 # Ensure parse_etching function is called correctly
                 # Assuming parse_etching exists and returns EtchingData or None
                 etching_result = parse_etching(fields)
                 if isinstance(etching_result, EtchingData):
                     runestone.etching = etching_result
                 elif etching_result is not None: # Handle potential incorrect return type
                      print(f"Warning: parse_etching returned unexpected type {type(etching_result)}. Expected EtchingData or None.")

        # Parse Mint details - Use correct case for Mint
        if RuneTag.Mint in fields and len(fields[RuneTag.Mint]) == 2:
             # Ensure values are integers before creating RuneId
             try:
                 block_val = int(fields[RuneTag.Mint][0])
                 tx_val = int(fields[RuneTag.Mint][1])
                 runestone.mint = RuneId(block=block_val, tx=tx_val)
             except (ValueError, TypeError):
                 print(f"Warning: Invalid values for Mint tag: {fields[RuneTag.Mint]}. Expected two integers.")
                 runestone.cenotaph = True
        elif RuneTag.Mint in fields: # If Mint tag exists but has wrong number of values
             print(f"Warning: Incorrect number of values for Mint tag. Expected 2, got {len(fields[RuneTag.Mint])}.")
             runestone.cenotaph = True # Invalid mint structure

        # Parse Pointer details - Use correct case for Pointer
        if RuneTag.Pointer in fields and len(fields[RuneTag.Pointer]) == 1:
            # Ensure value is an integer
            try:
                runestone.pointer = int(fields[RuneTag.Pointer][0])
            except (ValueError, TypeError):
                print(f"Warning: Invalid value for Runestone Pointer tag: {fields[RuneTag.Pointer][0]}. Expected integer.")
                runestone.cenotaph = True
        elif RuneTag.Pointer in fields: # If Pointer tag exists but has wrong number of values
            print(f"Warning: Incorrect number of values for Runestone Pointer tag. Expected 1, got {len(fields[RuneTag.Pointer])}.")
            runestone.cenotaph = True

    # Process Edicts if BODY tag was present and not Cenotaph - Use correct case for Body
    if RuneTag.Body in fields and not runestone.cenotaph:
        # Ensure parse_runes_delta_encoded_edicts exists and returns List[Edict] or None
        edicts_result = parse_runes_delta_encoded_edicts(fields[RuneTag.Body])
        if isinstance(edicts_result, list):
            runestone.edicts = edicts_result
        elif edicts_result is None: # Check if parsing failed
             print("Marking as Cenotaph due to Edict parsing error (returned None).")
             runestone.cenotaph = True
             runestone.edicts = [] # Reset edicts
        else:
            print(f"Warning: parse_runes_delta_encoded_edicts returned unexpected type {type(edicts_result)}. Expected list or None.")
            runestone.cenotaph = True
            runestone.edicts = [] # Reset edicts

    # Process collected JS-style protocol payloads if any
    all_protostone_payloads_decoded = []
    if js_unpacked_protocol_payloads and not runestone.cenotaph:
        #print(f"\n--- Processing {len(js_unpacked_protocol_payloads)} potential JS-style protocol payloads ---")
        packed_protocol_bytes = bytearray()
        for integer_payload in js_unpacked_protocol_payloads:
            try:
                # Validate integer is non-negative before packing
                if not isinstance(integer_payload, int) or integer_payload < 0:
                     raise ValueError(f"Invalid payload value: {integer_payload}")
                packed_chunk = pack_like_js_unpack(integer_payload)
                packed_protocol_bytes.extend(packed_chunk)
            except ValueError as e:
                 print(f"Warning: Skipping invalid integer {integer_payload} during JS-style packing: {e}")

        if packed_protocol_bytes:
            #print(f"Re-packed JS-style protocol bytes (length {len(packed_protocol_bytes)}): {packed_protocol_bytes.hex()}")
            try:
                # Use the existing decode_leb128 function
                all_protostone_payloads_decoded = decode_leb128(bytes(packed_protocol_bytes))
                #print(f"Decoded LEB128 from re-packed bytes: {all_protostone_payloads_decoded}")
            except Exception as e:
                print(f"Error decoding LEB128 from re-packed JS-style bytes: {e}. Treating as Cenotaph.")
                runestone.cenotaph = True
                all_protostone_payloads_decoded = [] # Clear the list
        else:
             print("No valid data after attempting JS-style packing.")
             if js_unpacked_protocol_payloads: # If we started with payloads but ended with none
                  print("Marking as Cenotaph due to packing failure.")
                  runestone.cenotaph = True

    # --- Now, parse the Protostones using the decoded payload list ---
    if all_protostone_payloads_decoded and not runestone.cenotaph:
        #print(f"\n--- Parsing Protostones from decoded payload list ---")
        parsed_protostones_list = []
        proto_payload_idx = 0
        while proto_payload_idx < len(all_protostone_payloads_decoded):
            # Check if enough data for protocol_id and length
            if proto_payload_idx + 1 >= len(all_protostone_payloads_decoded):
                #print(f"Warning: Not enough data left for Protostone ID and length at index {proto_payload_idx}. Stopping.")
                runestone.residual_data.extend(all_protostone_payloads_decoded[proto_payload_idx:])
                break

            protocol_id = all_protostone_payloads_decoded[proto_payload_idx]
            payload_len = all_protostone_payloads_decoded[proto_payload_idx + 1]

            # Calculate the expected end index for this potential protostone
            start_index = proto_payload_idx + 2
            end_index = start_index + payload_len

            # *** Check for Protocol ID 0 (Padding/Empty Protostone) ***
            if protocol_id == 0:
                #print(f"Skipping Protostone at index {proto_payload_idx} with Protocol ID 0 (likely padding). Declared length: {payload_len}")
                # Check if the declared length matches available data for this padding stone
                if end_index > len(all_protostone_payloads_decoded):
                     #print(f"Warning: Padding Protostone (ID 0) declared length {payload_len} exceeds available data. Data might be corrupt.")
                     runestone.residual_data.extend(all_protostone_payloads_decoded[start_index:])
                     runestone.cenotaph = True # Treat as error
                     break
                elif payload_len > 0:
                     # Consume the declared payload even if it's padding
                     #print(f"  -> Consuming {payload_len} integers for padding Protostone.")
                     pass # Index will be advanced below

                proto_payload_idx = end_index # Move index past this padding protostone
                continue # Skip to the next iteration

            # --- Process Non-Zero Protocol ID ---
            #print(f"\nAttempting to parse Protostone - Protocol ID: {protocol_id}, Declared Length: {payload_len}")

            available_data_len = len(all_protostone_payloads_decoded) - start_index

            if end_index > len(all_protostone_payloads_decoded):
                #print(f"Error: Declared Protostone length ({payload_len}) for ID {protocol_id} exceeds available data (have {available_data_len}). Marking as Cenotaph.")
                runestone.cenotaph = True
                runestone.residual_data.extend(all_protostone_payloads_decoded[start_index:])
                break # Stop parsing protostones

            protostone_data_segment = all_protostone_payloads_decoded[start_index:end_index]
            actual_len = len(protostone_data_segment)

            if actual_len != payload_len:
                 #print(f"Error: Protostone (ID {protocol_id}) declared length {payload_len} but actual data length is {actual_len}. Marking as Cenotaph.")
                 runestone.cenotaph = True
                 runestone.residual_data.extend(protostone_data_segment)
                 break # Stop parsing protostones

            #print(f"Parsing Protostone data segment: {protostone_data_segment}")

            try:
                parsed_stone_obj, _ = parse_protocol_fields(protostone_data_segment, protocol_id)

                if parsed_stone_obj:
                    parsed_protostones_list.append(parsed_stone_obj)
                    #print(f"Successfully parsed Protostone: {parsed_stone_obj}")
                else:
                     #print(f"Error: Failed to parse Protostone segment (ID {protocol_id}, returned None): {protostone_data_segment}. Marking as Cenotaph.")
                     runestone.cenotaph = True
                     runestone.residual_data.extend(protostone_data_segment)
                     break

            except Exception as e:
                #print(f"Error during call to parse_protocol_fields for segment (ID {protocol_id}) {protostone_data_segment}: {e}. Marking as Cenotaph.")
                runestone.cenotaph = True
                runestone.residual_data.extend(protostone_data_segment)
                break

            # Move index to the start of the next potential Protostone
            proto_payload_idx = end_index

        # Assign the list of successfully parsed (non-padding) protostone objects
        runestone.protostones = parsed_protostones_list

    elif js_unpacked_protocol_payloads and runestone.cenotaph:
        #print("Runestone marked as Cenotaph before Protostone parsing. Skipping Protostone processing.")
        runestone.residual_data.extend(js_unpacked_protocol_payloads) # Add raw payloads as residual


    #print(f"\n--- Final Runestone Parsing Result ---")
    # Ensure Edicts are empty if Cenotaph
    if runestone.cenotaph:
         runestone.edicts = []
         runestone.protostones = []
    #print(f"Parsed Runestone: {runestone}")

    return runestone

# Ensure parse_etching_fields and parse_edicts are defined elsewhere or add stubs
# def parse_etching_fields(fields): return None # Placeholder
# def parse_edicts(body_payload): return [] # Placeholder
# Assuming parse_protocol_fields is defined and correctly parses a single Protostone payload list

# ... (rest of the file) ...

# Modify the RunestoneContainer if needed to include residual_data and cenotaph flag
# @dataclass
# class RunestoneContainer:
#    ...

# Modify ProtoStone if needed
# @dataclass
# class ProtoStone:
#    ...

# Update parse_protocol_fields signature and return type (Commented out as it's defined above)
# def parse_protocol_fields(payload: List[int], protocol_id: int) -> Tuple[Optional[ProtoStone], List[int]]:
    # ... implementation ...

# ------------ 输出函数 (合并) ------------

def print_detailed_runestone(runestone_result):
    """打印解析后的 Runestone 的详细信息 (来自 alkanes_parser.py, 适配新结构)"""
    print("\n==== 解析出的 Runestone 详细信息 ====")
    
    # 打印 Runes Edicts
    if runestone_result.get('edicts'):
        print("Runes Edicts (顶层):")
        for i, edict in enumerate(runestone_result['edicts']):
            print(f"  - Edict #{i+1}: {edict}")
    else:
        print("Runes Edicts (顶层): None")
    
    # 打印 Etching
    etching_info = runestone_result.get('etching')
    if etching_info:
        print("\nEtching 详情:")
        # 格式化 Terms 中的 amount
        if etching_info.get('terms') and etching_info['terms'].get('amount_raw') is not None:
             div = etching_info.get('divisibility', 0)
             etching_info['terms']['amount'] = format_amount(etching_info['terms']['amount_raw'], div)
             # 可以选择删除 amount_raw
             # del etching_info['terms']['amount_raw']
        
        for k, v in etching_info.items():
            # 特别格式化 supply
            if k == 'supply' and v is not None:
                 div = etching_info.get('divisibility', 0)
                 print(f"  {k}: {v} ({format_amount(v, div)})")
            # 特别处理 terms
            elif k == 'terms' and v:
                print(f"  {k}:")
                for tk, tv in v.items():
                     print(f"    {tk}: {tv}")
            else:
                print(f"  {k}: {v}")
    else:
        print("\nEtching: None")
    
    # 打印 Mint
    mint_info = runestone_result.get('mint')
    if mint_info:
        print(f"\nMint Rune ID: {mint_info}")
    else:
        print("\nMint: None")
    
    # 打印 Runes Pointer
    pointer_info = runestone_result.get('pointer')
    print(f"\nRunes Pointer: {pointer_info if pointer_info is not None else 'None'}")
    
    # 打印 Protostones
    protostones_list = runestone_result.get('protostones')
    if protostones_list:
        print("\nProtostones 详情:")
        for i, protostone in enumerate(protostones_list):
            print(f"\n--- Protostone #{i+1} ---")
            print(protostone) # 使用 Protostone 的 __str__ 方法
    else:
        print("\nProtostones: None")

# --- 新添加的编码函数 ---
def encode_leb128(value: int) -> bytes:
    """将单个非负整数编码为 LEB128 字节串"""
    if value < 0:
        raise ValueError("LEB128 encoding only supports non-negative integers")
    if value == 0:
        return b'\x00'

    result = bytearray()
    while value > 0:
        byte = value & 0x7f # 取低 7 位
        value >>= 7
        if value > 0:
            byte |= 0x80 # 设置最高位表示还有后续字节
        result.append(byte)
    return bytes(result)

def encode_alkanes_call(call_data: dict) -> int:
    """
    将 Alkanes 调用数据 {'opcode': opcode, 'alkaneId': {'block': b, 'tx': t}}
    编码回 Protostone Message 字段所需的整数。
    过程：[block, tx, opcode] -> LEB128编码每个元素 -> 连接字节 -> 字节转回整数
    """
    if not call_data or 'opcode' not in call_data or 'alkaneId' not in call_data:
        raise ValueError("无效的 Alkanes 调用数据结构")

    alkane_id = call_data['alkaneId']
    opcode = call_data['opcode']
    block = alkane_id.get('block')
    tx = alkane_id.get('tx')

    if block is None or tx is None:
         raise ValueError("Alkanes 调用数据缺少 block 或 tx")

    # 1. 构建整数列表
    call_ints = [block, tx, opcode]
    # print(f"【编码Alkanes Call】整数列表: {call_ints}")

    # 2. 对每个整数进行 LEB128 编码
    encoded_bytes_list = [encode_leb128(i) for i in call_ints]
    # for i, b in zip(call_ints, encoded_bytes_list):
    #      print(f"  -> {i} 编码为: {b.hex()}")

    # 3. 连接编码后的字节串
    final_bytes = b"".join(encoded_bytes_list)
    # print(f"【编码Alkanes Call】连接后字节: {final_bytes.hex()}")

    # 4. 将连接后的字节串转回单个整数 (小端序)
    message_field_int = int.from_bytes(final_bytes, 'little', signed=False)
    # print(f"【编码Alkanes Call】最终 Message 字段整数: {message_field_int}")

    return message_field_int

def encode_edicts(edicts: list[Edict], is_protocol_edict: bool) -> list[int]:
    """
    将 Edict 列表编码为 Delta-encoded 的整数列表。
    Args:
        edicts: Edict 对象列表。
        is_protocol_edict: 指示是否为 Protostone 内的 Edict (目前无特殊处理，但保留参数)。
    Returns:
        包含 [block_delta, tx_val, amount, output, ...] 的整数列表。
    """
    if not edicts:
        return []

    # print(f"【编码 Edicts】输入 Edict 数量: {len(edicts)}")

    # 1. 排序 Edicts (首先按 block, 然后按 tx)
    def sort_key(edict: Edict):
        if edict.rune_id is None:
             return (float('inf'), float('inf'))
        block = int(edict.rune_id.block) if edict.rune_id.block is not None else float('inf')
        tx = int(edict.rune_id.tx) if edict.rune_id.tx is not None else float('inf')
        return (block, tx)

    edicts.sort(key=sort_key)
    # print("  -> Edicts 排序后:")
    # for ed in edicts: print(f"    - {ed}")

    # 2. Delta 编码
    encoded_ints = []
    last_block = 0
    last_tx = 0
    for edict in edicts:
         if edict.rune_id is None or edict.rune_id.block is None or edict.rune_id.tx is None:
              print("【警告】跳过 RuneId 或其属性为 None 的 Edict") # 保留警告
              continue
         current_block = int(edict.rune_id.block)
         current_tx = int(edict.rune_id.tx)

         if current_block == last_block:
             block_delta = 0
             tx_val = current_tx - last_tx
             if tx_val < 0:
                  print(f"【警告】计算出的 TX 增量为负: {tx_val} (current={current_tx}, last={last_tx}). 可能Edict排序或数据有问题。") # 保留警告
         else:
             block_delta = current_block - last_block
             tx_val = current_tx
             if block_delta < 0:
                  print(f"【警告】计算出的 Block 增量为负: {block_delta} (current={current_block}, last={last_block}). 可能Edict排序或数据有问题。") # 保留警告

         encoded_ints.extend([block_delta, tx_val, edict.amount, edict.output])
         # print(f"  -> 编码 Edict {edict}: BlockDelta={block_delta}, TxVal={tx_val}, Amount={edict.amount}, Output={edict.output}")

         last_block = current_block
         last_tx = current_tx

    # print(f"【编码 Edicts】输出整数列表: {encoded_ints}")
    return encoded_ints

def encode_protostone(protostone: ProtoStone) -> bytes:
    """
    将 Protostone 对象编码为 LEB128 字节流。
    """
    if protostone.protocol_id is None:
        raise ValueError("Protostone 必须包含 protocol_id")

    # print(f"【编码 Protostone】Protocol ID: {protostone.protocol_id}")

    protostone_fields_ints = []

    # 1. 添加字段 (标签 + 值)，注意处理 Option 类型 (None 值不添加)
    if protostone.pointer is not None:
        protostone_fields_ints.extend([ProtoTag.Pointer, protostone.pointer])
        # print(f"  -> 添加 Pointer: Tag={ProtoTag.Pointer}, Value={protostone.pointer}")

    if protostone.refund_pointer is not None:
        protostone_fields_ints.extend([ProtoTag.Refund, protostone.refund_pointer])
        # print(f"  -> 添加 Refund Pointer: Tag={ProtoTag.Refund}, Value={protostone.refund_pointer}")

    if protostone.burn is not None:
        protostone_fields_ints.extend([ProtoTag.Burn, protostone.burn])
        # print(f"  -> 添加 Burn: Tag={ProtoTag.Burn}, Value={protostone.burn}")

    # 处理 Message 或 Alkanes Call
    if protostone.alkane_call is not None and protostone.protocol_id == 1:
        try:
            alkane_call_int = encode_alkanes_call(protostone.alkane_call)
            protostone_fields_ints.extend([ProtoTag.Message, alkane_call_int])
            # print(f"  -> 添加 Alkanes Call (via Message): Tag={ProtoTag.Message}, Value={alkane_call_int}")
        except ValueError as e:
             print(f"【警告】编码 Alkanes Call 失败: {e}，跳过 Message 字段") # 保留警告
    elif protostone.message is not None:
        try:
            message_int = int.from_bytes(protostone.message, 'little', signed=False)
            protostone_fields_ints.extend([ProtoTag.Message, message_int])
            # print(f"  -> 添加通用 Message: Tag={ProtoTag.Message}, Value={message_int} (from bytes: {protostone.message.hex()})")
        except Exception as e:
            print(f"【警告】转换通用 Message 字节为整数失败: {e}，跳过 Message 字段") # 保留警告

    # 处理 From 索引列表
    if protostone.from_indices:
        for index in protostone.from_indices:
            protostone_fields_ints.extend([ProtoTag.From, index])
            # print(f"  -> 添加 From Index: Tag={ProtoTag.From}, Value={index}")

    # 2. 添加 Body 和 Edicts (如果存在)
    if protostone.edicts:
        protostone_fields_ints.append(ProtoTag.Body) # 添加 Body 标签
        # print(f"  -> 添加 Body Tag: {ProtoTag.Body}")
        edict_ints = encode_edicts(protostone.edicts, is_protocol_edict=True)
        protostone_fields_ints.extend(edict_ints) # 追加 Edict 编码后的整数列表
        # print(f"  -> 追加编码后的 Edicts (整数): {edict_ints}")

    # 3. 计算 Length
    length = len(protostone_fields_ints)
    # print(f"【编码 Protostone】计算得到的 Length: {length}")

    # 4. 构建最终整数列表
    final_ints = [protostone.protocol_id, length] + protostone_fields_ints
    # print(f"【编码 Protostone】最终整数列表: {final_ints}")

    # 5. LEB128 编码并连接字节
    encoded_bytes_list = [encode_leb128(i) for i in final_ints]
    # print("  -> LEB128 编码各个整数:")
    # for i, b in zip(final_ints, encoded_bytes_list):
    #      print(f"    - {i} -> {b.hex()}")
    final_protostone_bytes = b"".join(encoded_bytes_list)
    # print(f"【编码 Protostone】最终字节流: {final_protostone_bytes.hex()}")

    return final_protostone_bytes

def decode_alkanes_protostone_from_runestone(runestone_hex: str) -> ProtoStone | None:
    """
    解码 Runestone 十六进制字符串，提取并返回第一个 Alkanes Protostone 对象。
    """
    print(f"\n--- 解码 Runestone 以提取 Protostone ---")
    print(f"输入 Hex: {runestone_hex}")
    parsed_result = parse_runestones(runestone_hex) # 内部有打印，已被清理
    if parsed_result and parsed_result.get('protostones'):
        # 假设我们只关心第一个 Protostone
        protostone_obj = parsed_result['protostones'][0]
        # 验证 Protocol ID 是否为 Alkanes (1)
        if protostone_obj.protocol_id == 1:
            print("解码成功，找到 Alkanes Protostone:")
            print(protostone_obj)
            return protostone_obj
        else:
            print(f"找到 Protostone，但 Protocol ID ({protostone_obj.protocol_id}) 不是 Alkanes (1)")
            return None
    else:
        print("解码失败，未在 Runestone 中找到 Protostone")
        return None

def encode_alkanes_protostone_to_runestone(protostone: ProtoStone) -> str | None:
    """
    将 Alkanes Protostone 对象编码到一个最小化的 Runestone 十六进制字符串中。
    """
    print(f"\n--- 将 Protostone 对象编码为 Runestone Hex ---")
    if protostone.protocol_id != 1:
        print("【错误】此函数仅用于编码 Alkanes Protostone (Protocol ID 1)")
        return None

    print("输入 Protostone 对象:")
    print(protostone)

    # 1. 编码 Protostone 对象获取其字节流
    print("\n步骤 1: 编码 Protostone 对象为字节...")
    protostone_bytes = encode_protostone(protostone) # 内部打印已清理
    if not protostone_bytes:
        print("【错误】编码 Protostone 失败")
        return None
    print(f"  -> Protostone 字节 (Hex): {protostone_bytes.hex()}")

    # 2. 将 Protostone 字节流分块并转换为整数列表 (嵌入 Runestone)
    print("\n步骤 2: 将 Protostone 字节分块并嵌入 Runestone 结构...")
    runestone_ints = []
    MAX_CHUNK_SIZE = 15 # 每个 Protocol 字段值最多代表 15 字节
    i = 0
    while i < len(protostone_bytes):
        chunk = protostone_bytes[i : i + MAX_CHUNK_SIZE]
        chunk_int = int.from_bytes(chunk, 'little', signed=False)
        runestone_ints.extend([RuneTag.Protocol, chunk_int])
        print(f"  -> Chunk {i//MAX_CHUNK_SIZE + 1}: bytes={chunk.hex()}, int={chunk_int}")
        i += MAX_CHUNK_SIZE
    print(f"  -> 构建的 Runestone 整数列表: {runestone_ints}")

    # 3. 对 Runestone 整数列表进行 LEB128 编码
    print("\n步骤 3: LEB128 编码 Runestone 整数列表...")
    final_runestone_bytes_list = [encode_leb128(val) for val in runestone_ints]
    # for val, b in zip(runestone_ints, final_runestone_bytes_list):
    #     print(f"    - {val} -> {b.hex()}")

    # 4. 连接字节并转换为十六进制字符串
    final_runestone_bytes = b"".join(final_runestone_bytes_list)
    final_runestone_hex = final_runestone_bytes.hex()
    print("\n步骤 4: 连接字节并转为 Hex...")
    print(f"  -> 最终 Runestone 字节 (Hex): {final_runestone_hex}")

    return final_runestone_hex

def pack_like_js_unpack(value: int) -> bytes:
    """
    Simulates the inverse of the JS unpack function's core logic for a single value.
    Converts an integer to its 15-byte, little-endian representation, padded with zeros.
    """
    if value < 0:
        raise ValueError("Value cannot be negative")
    try:
        # Convert to hex, remove '0x' prefix
        hex_str = hex(value)[2:]
        # Pad with leading zeros to 30 characters (15 bytes)
        padded_hex = hex_str.zfill(30)
        # Convert hex string to bytes
        byte_data = bytes.fromhex(padded_hex)
        # Reverse the byte order (little-endian)
        return byte_data[::-1]
    except Exception as e:
        print(f"Error packing value {value} like JS unpack: {e}")
        # Return 15 zero bytes as a fallback to avoid crashing
        return b'\x00' * 15

def unpack_like_js(data: bytes) -> list[int]:
    """
    Simulates the JS unpack function.
    Splits data into 15-byte chunks, reverses bytes in each chunk, and converts to integer.
    """
    integers = []
    chunk_size = 15
    num_chunks = math.ceil(len(data) / chunk_size)

    for i in range(num_chunks):
        start = i * chunk_size
        end = start + chunk_size
        chunk = data[start:end]

        # The JS logic effectively processes chunks as found. If a chunk is short,
        # it processes those bytes. Let's replicate that directly.

        # Reverse the bytes in the chunk
        reversed_chunk = chunk[::-1]

        # Convert reversed bytes to hex string
        hex_str = reversed_chunk.hex()

        # Convert hex string to integer
        if hex_str: # Avoid converting empty string
            try:
                integers.append(int(hex_str, 16))
            except ValueError:
                print(f"Warning: Could not convert hex '{hex_str}' from chunk {i+1} to integer.")
                # Skip problematic chunks for now
                pass
        elif len(chunk) > 0:
             # Chunk had only zero bytes
             integers.append(0)

    return integers

# ------------ 主函数 (更新) ------------
def test_with_known_data(): # 保留定义，但不调用
    # 这个数据现在应该能正确解析出 Protostone (包括Edict)
    alkanes_test = "ff7f818cec82d08bc0a882c1d215"
    print("\n============== 测试实际Alkanes数据 (应含Edict) ==============")
    print(f"【测试数据】: {alkanes_test}")
    sys.stdout.flush()
    try:
        result = parse_runestones(alkanes_test)
        print_detailed_runestone(result)
    except Exception as e:
        print(f"【错误】解析过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

# ------------ 编码函数 (新) ------------

# --- Encoding Helper Functions ---

def encode_etching_fields(etching: Optional[EtchingData]) -> List[int]:
    """Encodes EtchingData back into a list of tag-value integer pairs."""
    if etching is None:
        return []

    etching_ints = []
    flags = 0
    flags |= (1 << RuneFlag.Etching) # Set Etching flag

    if etching.terms:
        flags |= (1 << RuneFlag.Terms) # Set Terms flag

    if etching.turbo:
        flags |= (1 << RuneFlag.Turbo) # Set Turbo flag

    etching_ints.extend([RuneTag.Flags, flags])

    if etching.rune_int is not None:
        etching_ints.extend([RuneTag.Rune, etching.rune_int])
    if etching.divisibility > 0:
        etching_ints.extend([RuneTag.Divisibility, etching.divisibility])
    if etching.spacers > 0:
         etching_ints.extend([RuneTag.Spacers, etching.spacers])
    if etching.symbol is not None:
        symbol_int = ord(etching.symbol)
        etching_ints.extend([RuneTag.Symbol, symbol_int])

    # Handle Premine - needs raw int or robust parsing
    if hasattr(etching, 'premine_raw_int') and etching.premine_raw_int > 0:
          etching_ints.extend([RuneTag.Premine, etching.premine_raw_int])
    elif etching.premine and etching.premine != "0":
         print("Warning: Encoding premine requires raw integer value or robust parsing from string.")
         # Add placeholder logic or raise error if needed

    if etching.terms:
        terms = etching.terms
        # Handle Amount - needs raw int or robust parsing
        if terms.get('amount_raw') is not None and terms['amount_raw'] > 0:
             etching_ints.extend([RuneTag.Amount, terms['amount_raw']])
        elif terms.get('amount') and terms['amount'] != "0":
             print("Warning: Encoding terms amount requires raw integer value or robust parsing from string.")

        if terms.get('cap') is not None:
            etching_ints.extend([RuneTag.Cap, terms['cap']])
        if terms.get('height') and terms['height'][0] is not None:
            etching_ints.extend([RuneTag.HeightStart, terms['height'][0]])
        if terms.get('height') and terms['height'][1] is not None:
            etching_ints.extend([RuneTag.HeightEnd, terms['height'][1]])
        if terms.get('offset') and terms['offset'][0] is not None:
            etching_ints.extend([RuneTag.OffsetStart, terms['offset'][0]])
        if terms.get('offset') and terms['offset'][1] is not None:
            etching_ints.extend([RuneTag.OffsetEnd, terms['offset'][1]])

    return etching_ints

def encode_mint_id(mint: Optional[RuneId]) -> List[int]:
    """Encodes RuneId for Mint into [Tag.Mint, block, Tag.Mint, tx]."""
    if mint is None:
        return []
    return [RuneTag.Mint, mint.block, RuneTag.Mint, mint.tx]

def encode_pointer(pointer: Optional[int]) -> List[int]:
    """Encodes the Runestone pointer."""
    if pointer is None:
        return []
    return [RuneTag.Pointer, pointer]


# --- Main Encoding Function ---
def encode_runestone_with_protostones(runestone: RunestoneContainer) -> Optional[str]:
    """
    Encodes a RunestoneContainer object (potentially with Protostones)
    into a Runestone hex string, simulating the JS SDK's packing logic for Protostones.
    """
    print("\n--- Encoding RunestoneContainer to Hex (JS SDK Simulation) ---")
    print(f"Input RunestoneContainer: {runestone}")

    runestone_ints = []

    # 1. Encode Etching, Mint, Pointer
    etching_ints = encode_etching_fields(runestone.etching)
    runestone_ints.extend(etching_ints)

    mint_ints = encode_mint_id(runestone.mint)
    runestone_ints.extend(mint_ints)

    pointer_ints = encode_pointer(runestone.pointer)
    runestone_ints.extend(pointer_ints)

    # 2. Encode Protostones using the JS SDK simulation
    if runestone.protostones:
        print("\n  Encoding Protostones (JS Style)...")
        all_protostone_int_payloads = []
        for i, proto in enumerate(runestone.protostones):
            print(f"    Encoding Protostone #{i+1} (ID: {proto.protocol_id})...")
            try:
                # Get the list of integers representing the protostone payload
                # (This involves encoding to bytes and decoding back, as per encode_protostone logic)
                # Alternatively, build the int list directly before the final LEB128 encoding step in encode_protostone
                # Let's reuse encode_protostone but extract the int list before the final LEB128 step

                # --- Re-implement part of encode_protostone to get the int list --- 
                if proto.protocol_id is None: raise ValueError("Missing protocol_id")
                proto_fields_ints = []
                if proto.pointer is not None: proto_fields_ints.extend([ProtoTag.Pointer, proto.pointer])
                if proto.refund_pointer is not None: proto_fields_ints.extend([ProtoTag.Refund, proto.refund_pointer])
                if proto.burn is not None: proto_fields_ints.extend([ProtoTag.Burn, proto.burn])
                if proto.alkane_call is not None and proto.protocol_id == 1:
                    alkane_call_int = encode_alkanes_call(proto.alkane_call)
                    proto_fields_ints.extend([ProtoTag.Message, alkane_call_int])
                elif proto.message is not None:
                    message_int = int.from_bytes(proto.message, 'little', signed=False)
                    proto_fields_ints.extend([ProtoTag.Message, message_int])
                if proto.from_indices:
                    for index in proto.from_indices: proto_fields_ints.extend([ProtoTag.From, index])
                if proto.edicts:
                    proto_fields_ints.append(ProtoTag.Body)
                    edict_ints = encode_edicts(proto.edicts, is_protocol_edict=True)
                    proto_fields_ints.extend(edict_ints)
                length = len(proto_fields_ints)
                proto_int_list_for_packing = [proto.protocol_id, length] + proto_fields_ints
                # --- End re-implementation ---

                print(f"      -> Ints for this Protostone: {proto_int_list_for_packing}")
                all_protostone_int_payloads.extend(proto_int_list_for_packing)
            except Exception as e:
                print(f"    Error preparing protostone #{i+1} int list: {e}. Skipping.")

        if all_protostone_int_payloads:
            print(f"    Combined Protostone Int Payloads for JS encipher: {all_protostone_int_payloads}")

            # Step 2 (JS Simulation): LEB128 encode all ints and concatenate bytes
            combined_leb128_bytes = b"".join([encode_leb128(val) for val in all_protostone_int_payloads])
            print(f"    Combined LEB128 Bytes (JS 'encipher' result, {len(combined_leb128_bytes)} bytes): {combined_leb128_bytes.hex()}")

            # Step 3 (JS Simulation): Unpack the combined bytes using 15-byte chunking
            unpacked_ints_for_runestone = unpack_like_js(combined_leb128_bytes)
            print(f"    Unpacked Ints for Runestone Payload (JS 'unpack' result): {unpacked_ints_for_runestone}")

            # Step 4 (JS Simulation): Add to runestone_ints with Protocol tag
            for unpacked_int in unpacked_ints_for_runestone:
                runestone_ints.extend([RuneTag.Protocol, unpacked_int])
        else:
            print("    No valid protostone payloads to encode.")

    # 3. Encode top-level Runes Edicts
    if runestone.edicts:
        print("\n  Encoding Runes Edicts...")
        edict_ints = encode_edicts(runestone.edicts, is_protocol_edict=False)
        runestone_ints.append(RuneTag.Body)
        runestone_ints.extend(edict_ints)

    # 4. Final LEB128 Encoding and Hex Conversion
    print(f"\n  Final Runestone Integer Payload: {runestone_ints}")
    final_bytes_list = [encode_leb128(val) for val in runestone_ints]
    final_bytes = b"".join(final_bytes_list)
    final_hex = final_bytes.hex()
    print(f"\n  Encoded Runestone Bytes ({len(final_bytes)} bytes): {final_bytes.hex()}")
    print(f"  Final Runestone Hex: {final_hex}")

    return final_hex

if __name__ == "__main__":
    print("=====================================================")
    print("Runestone + Protostone Parser/Encoder (JS SDK Style)")
    print("=====================================================")
    sys.stdout.flush()

    # --- 测试解码 ---
    # 这个是你之前提供的数据
    problematic_hex = "ff7f8192ec82d08b808082f6858090c0c12dff7fdd80c492e8bead01"
    print(f"\n--- 测试解码 ---")
    print(f"Input Hex: {problematic_hex}")

    parsed_runestone_container = None
    try:
        problematic_data_bytes = bytes.fromhex(problematic_hex)
        problematic_integers = decode_leb128(problematic_data_bytes)
        parsed_runestone_container = parse_runestones(problematic_integers)

        if parsed_runestone_container:
            print("\n--- 解码结果 ---")
            print(parsed_runestone_container)
            if parsed_runestone_container.cenotaph:
                print("===> Runestone 被标记为 Cenotaph")
            else:
                print(f"解析出的有效 Protostones: {len(parsed_runestone_container.protostones)}")
                for idx, stone in enumerate(parsed_runestone_container.protostones):
                    print(f"  Protostone {idx+1}:")
                    print(stone)
                if parsed_runestone_container.residual_data:
                     print(f"剩余未解析数据: {parsed_runestone_container.residual_data}")
        else:
            print("\n--- 解码失败 ---")

    except Exception as e:
        print(f"\n--- 解码过程中发生错误 --- ")
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


    # --- 测试编码 ---
    if parsed_runestone_container and not parsed_runestone_container.cenotaph:
        print("\n\n--- 测试编码 (基于解码结果) ---")
        re_encoded_hex = encode_runestone_with_protostones(parsed_runestone_container)

        if re_encoded_hex:
            print("\n--- 编码对比 ---")
            print(f"原始 Hex: {problematic_hex}")
            print(f"重编码 Hex: {re_encoded_hex}")
            if problematic_hex == re_encoded_hex:
                print("结果: 匹配! 🎉")
            else:
                print("结果: 不匹配! ❌")
                # Find difference
                diff_index = -1
                min_len = min(len(problematic_hex), len(re_encoded_hex))
                for i in range(0, min_len, 2):
                     if problematic_hex[i:i+2] != re_encoded_hex[i:i+2]:
                          diff_index = i
                          break
                if diff_index != -1:
                    print(f"差异首次出现在字节索引 {diff_index // 2}:")
                    print(f"  原始: ...{problematic_hex[max(0, diff_index-10):diff_index]}|{problematic_hex[diff_index:diff_index+2]}|{problematic_hex[diff_index+2:diff_index+12]}...")
                    print(f"  编码: ...{re_encoded_hex[max(0, diff_index-10):diff_index]}|{re_encoded_hex[diff_index:diff_index+2]}|{re_encoded_hex[diff_index+2:diff_index+12]}...")
                elif len(problematic_hex) != len(re_encoded_hex):
                     print(f"长度不匹配: 原始={len(problematic_hex)}, 编码={len(re_encoded_hex)}")

    elif parsed_runestone_container and parsed_runestone_container.cenotaph:
        print("\n--- 跳过编码测试 (解码结果为 Cenotaph) ---")
    else:
        print("\n--- 跳过编码测试 (解码失败) ---")


    sys.stdout.flush()


    # --- 测试手动构造并编码 ---
    print("\n\n--- 测试手动构造数据并编码 (修正) ---")
    try:
        # 手动创建第一个 Protostone (根据日志修正)
        edict1 = Edict(rune_id=RuneId(block=2, tx=59), amount=1, output=0)
        proto1 = ProtoStone(
            protocol_id=1,
            pointer=0,
            refund_pointer=0,
            edicts=[edict1], # 包含 Edict
            alkane_call=None, # 不包含 Alkanes Call
            burn=None,
            message=None,
            from_indices=[],
            residual_tags={}
        )
        print("\nManually created ProtoStone 1 (Corrected):")
        print(proto1)

        # 手动创建第二个 Protostone (根据日志确认)
        proto2 = ProtoStone(
            protocol_id=1,
            pointer=0,
            refund_pointer=0,
            alkane_call={'opcode': 77, 'alkaneId': {'block': 2, 'tx': 59}}, # 包含 Alkanes Call
            edicts=[], # 不包含 Edicts
            burn=None,
            message=None,
            from_indices=[],
            residual_tags={}
        )
        print("\nManually created ProtoStone 2 (Confirmed):")
        print(proto2)

        # 手动创建 RunestoneContainer (保持不变, 只包含这两个Protostone)
        manual_runestone = RunestoneContainer(
            protostones=[proto1, proto2],
            mint=None,
            pointer=None,
            edicts=[],
            etching=None,
            cenotaph=False,
            residual_data=[]
        )
        print("\nManually created RunestoneContainer:")
        print(manual_runestone)

        # 使用手动创建的对象进行编码
        manual_encoded_hex = encode_runestone_with_protostones(manual_runestone)

        # 对比编码结果
        if manual_encoded_hex:
            print("\n--- 手动构造编码对比 ---")
            expected_hex = "ff7f8192ec82d08b808082f6848090c0c12dff7fdd80c492e89ead01" # 原始 problematic_hex
            print(f"预期 Hex: {expected_hex}")
            print(f"编码 Hex: {manual_encoded_hex}")
            if expected_hex == manual_encoded_hex:
                print("结果: 匹配! ✅")
            else:
                print("结果: 不匹配! ❌")
                # Find difference (Optional: add back the diff finding code if needed)
        else:
            print("\n--- 手动构造编码失败 ---")

    except Exception as e:
        print(f"\n--- 手动构造测试过程中发生错误 --- ")
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()


    sys.stdout.flush()