#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的BRC20 API接口
"""

import asyncio
import httpx
import json
from urllib.parse import quote, unquote

# 新API配置
NEW_API_URL = "http://47.242.55.181:3340"
NEW_API_PATH = "/v1/brc20/tick_info"

async def test_new_api(tick: str):
    """测试新的API接口"""
    print(f"🧪 测试新API接口 - tick: {tick}")
    
    try:
        async with httpx.AsyncClient() as client:
            # URL编码
            encoded_tick = quote(tick, safe='%')
            full_url = f"{NEW_API_URL}{NEW_API_PATH}?ticker={encoded_tick}"
            
            print(f"📡 请求URL: {full_url}")
            
            response = await client.get(full_url)
            print(f"📊 响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 响应数据:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                
                # 检查新格式
                if data.get("error") is None and "result" in data:
                    result = data["result"]
                    print(f"\n📋 解析后的关键信息:")
                    print(f"   Tick: {result.get('tick', 'N/A')}")
                    print(f"   Max Supply: {result.get('max_supply', 'N/A')}")
                    print(f"   Minted Supply: {result.get('minted_supply', 'N/A')}")
                    print(f"   Remaining Supply: {result.get('remaining_supply', 'N/A')}")
                    print(f"   Decimals: {result.get('decimals', 'N/A')}")
                    print(f"   Limit Per Mint: {result.get('limit_per_mint', 'N/A')}")
                    print(f"   Is Self Mint: {result.get('is_self_mint', 'N/A')}")
                    print(f"   Deploy Block Height: {result.get('deploy_block_height', 'N/A')}")
                    print(f"   Is Fully Minted: {result.get('is_fully_minted', 'N/A')}")
                    return True
                else:
                    print(f"❌ 响应格式不正确")
                    return False
            else:
                print(f"❌ API请求失败: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"错误信息: {error_data}")
                except:
                    print(f"错误内容: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return False

async def test_multiple_ticks():
    """测试多个tick"""
    test_ticks = ["jude", "Jude", "JUDE", "ordi", "sats"]
    
    print("🚀 开始测试多个tick...")
    print("=" * 50)
    
    for tick in test_ticks:
        print(f"\n{'='*20} 测试 {tick} {'='*20}")
        success = await test_new_api(tick)
        if success:
            print(f"✅ {tick} 测试成功")
        else:
            print(f"❌ {tick} 测试失败")
        print("-" * 50)

async def main():
    """主函数"""
    print("🔧 BRC20 API 接口测试工具")
    print("=" * 50)
    
    # 首先测试jude
    print("\n📋 测试目标tick: jude")
    await test_new_api("jude")
    
    print("\n" + "=" * 50)
    
    # 测试多个tick
    await test_multiple_ticks()
    
    print("\n🎯 测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
