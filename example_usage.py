#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量区块扫描脚本使用示例
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from batch_block_scanner import *

def example_small_range():
    """示例：扫描小范围区块"""
    print("示例：扫描包含已知交易的区块范围")
    print("=" * 50)
    
    # 扫描包含已知交易的区块附近
    start_block = 908119  # 包含已知交易的区块
    end_block = 908119    # 只扫描这一个区块
    
    print(f"扫描区块范围: {start_block} - {end_block}")
    print(f"查找条件:")
    print(f"  - witness数据长度 > 1000字符")
    print(f"  - 包含特定字符串: {TARGET_STRING[:20]}...")
    print()
    
    # 开始扫描
    records = batch_scan_blocks(start_block, end_block)
    
    if records:
        # 生成Excel文件
        filename = f"example_transactions_{start_block}_{end_block}.xlsx"
        create_excel_file(records, filename)
        
        print(f"\n✅ 扫描完成!")
        print(f"找到符合条件的交易: {len(records)} 笔")
        print(f"结果已保存到: {filename}")
        
        # 显示所有记录
        print(f"\n所有记录:")
        for i, record in enumerate(records, 1):
            print(f"  {i}. 区块:{record.block_height} 交易:{record.txid[:16]}...")
            print(f"     铸造地址: {record.mint_address}")
            print(f"     收货地址: {record.receive_address}")
    else:
        print(f"\n在指定区块范围内未找到符合条件的交易")

def example_check_dependencies():
    """检查依赖包"""
    print("\n检查依赖包:")
    print("-" * 30)
    
    required_packages = ['openpyxl', 'tqdm', 'requests']
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            print(f"❌ {package} - 未安装")
            print(f"   安装命令: pip install {package}")

def main():
    """主函数"""
    print("批量区块扫描脚本 - 使用示例")
    print("=" * 60)
    
    # 检查依赖
    example_check_dependencies()
    
    # 检查RPC连接
    print(f"\n检查RPC连接:")
    print("-" * 30)
    try:
        height = getblockcount()
        if height:
            print(f"✅ RPC连接正常，当前区块高度: {height}")
        else:
            print("❌ RPC连接失败")
            return
    except Exception as e:
        print(f"❌ RPC连接异常: {e}")
        return
    
    print()
    
    # 运行示例
    try:
        example_small_range()
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
    
    print(f"\n" + "=" * 60)
    print("示例完成")
    print("\n要运行完整的批量扫描，请使用:")
    print("  python batch_block_scanner.py")

if __name__ == "__main__":
    main()
