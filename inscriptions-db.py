# -*- coding: utf-8 -*-
import time
import requests
import json
import threading
import logging
import os
import re

from fastapi import FastAPI, Path, Query, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from pydantic import BaseModel
from typing import Union, List, Optional
import numpy as np
import math
from uvicorn.config import Config
from uvicorn.main import Server


# 连接本地比特币节点
rpc_user = 'forbtc'
rpc_password = 'loginbtc'
rpc_host = '127.0.0.1'
rpc_port = '8332'
rpc_url = f'http://{rpc_host}:{rpc_port}'
current_id = 1
stop_signal = threading.Event()

app = FastAPI()

inscriptions_cache = {}

# 非铭文ids
old_txids_general = set()

class InscriptionResult(BaseModel):
    txid: str
    type: str
    content: str
    output_address: str
    fee: float

class SearchResponse(BaseModel):
    total: int
    results: List[InscriptionResult]

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 封装一个日志打印函数
def log_info(*args):
    message = ' '.join(str(arg) for arg in args)
    logging.info(message)

def log_warning(*args):
    message = ' '.join(str(arg) for arg in args)
    logging.warning(message)

def log_error(*args):
    message = ' '.join(str(arg) for arg in args)
    logging.error(message)

@app.on_event("startup")
async def startup_event():
    log_info("应用程序启动")
    # 在这里执行应用程序启动时的操作

@app.on_event("shutdown")
async def shutdown_event():
    log_info("准备结束进程")
    stop_signal.set()
    listener_thread.join()

    #shutdown_save_data()

    log_info("应用程序关闭")

def shutdown_save_data():
    log_info("准备提取交易信息进行保存")
    # 从 inscriptions_cache 提取符文信息
    inscriptions_transactions = []
    for content, txs in inscriptions_cache.items():
        for txid, tx_info in txs.items():
            inscriptions_transactions.append({
                "txid": txid,
                "content": content,
                "output_address": tx_info['output_address']
            })

    # 保存符文交易信息
    save_inscriptions_transactions('old_inscriptions_transactions.json', inscriptions_transactions)
    log_info(f"保存了 {len(inscriptions_transactions)} 笔铭文交易")

    # 获取最新的内存池交易,由于rune_cache并没有进行最后一次获取,所以有一定概率会丢失小部分没统计到的新增符文
    new_mempool_txids = get_mempool_transactions()

    # 分离出普通交易
    inscriptions_txids = set(tx['txid'] for tx in inscriptions_transactions)
    normal_txids = new_mempool_txids - inscriptions_txids

    # 保存普通交易信息
    save_data_to_file('old_txids_general', list(normal_txids))
    log_info(f"保存了 {len(normal_txids)} 笔普通交易")

def rpc_call(method,params=[]):
    query = [
        {
            "id": current_id,
            "jsonrpc": "2.0",
            "method": method,
            "params": params
        }]
    responses = rpc_batch(query)
    for i, resp in enumerate(responses):
        data = resp['result']
        return data
    
def getblockcount():
    blockHeight = rpc_call('getblockcount',)
    return blockHeight

def getblockhash(blockHeight):
    blockhash = rpc_call('getblockhash',[blockHeight])
    return blockhash

# 获取区块交易
def getblock(blockhash):
    data = rpc_call('getblock',[blockhash,2])
    return data

# 从文件加载信息到内存
def load_data_from_file():
    global old_txids_general
    if os.path.exists('old_txids_general.json'):
        with open('old_txids_general.json', 'r', encoding='utf-8') as f:
            old_txids_general = set(json.load(f))

# 保存数据到文件
def save_data_to_file(filename = None,data = None):
    if filename:
        with open(f'{filename}.json', 'w', encoding='utf-8') as f:
            json.dump(list(data), f, indent=2, ensure_ascii=False)
    else:
        pass

def generate_intervals(fees, max_intervals=6):
    unique_fees = sorted(set(fees))
    if len(unique_fees) == 1:
        return [(unique_fees[0], unique_fees[0] + 1)]
    
    min_fee, max_fee = unique_fees[0], unique_fees[-1]
    interval_count = min(max_intervals, len(unique_fees))
    
    if interval_count == len(unique_fees):
        return [(fee, next_fee) for fee, next_fee in zip(unique_fees, unique_fees[1:] + [unique_fees[-1] + 1])]
    
    intervals = []
    start = min_fee
    for i in range(interval_count - 1):
        end = min_fee + (max_fee - min_fee) * (i + 1) // (interval_count - 1)
        intervals.append((start, end))
        start = end
    
    intervals.append((start, max_fee + 1))
    
    return intervals

@app.get("/api/search_inscriptions", response_model=SearchResponse)
async def search_inscriptions(
    content: str = Query(..., description="Content to search for in inscriptions"),
    type: Optional[str] = Query(None, description="Type of inscription to filter by")
):
    results = []
    # 复制字典内容到临时变量
    temp_cache = inscriptions_cache.copy()

    for txid, data in temp_cache.items():
        for inscription in data['inscriptions']:
            if type and inscription['type'] != type:
                continue
            if re.search(content, inscription.get('content', ''), re.IGNORECASE):
                results.append(InscriptionResult(
                    txid=txid,
                    type=inscription['type'],
                    content=inscription.get('content', ''),
                    output_address=data['output_address'],
                    fee=data['fee']
                ))

    if not results:
        raise HTTPException(status_code=404, detail="No matching inscriptions found")
    # 按照 fee 进行降序排序
    sorted_results = sorted(results, key=lambda x: x.fee, reverse=True)

    return SearchResponse(total=len(results), results=sorted_results)

def rpc_batch(requests_list):
    """
    执行批量RPC调用
    :param requests_list: 包含多个请求字典的列表
    :return: 响应列表
    """
    global current_id  # 声明使用全局变量 current_id

    payload = json.dumps(requests_list)

    # 更新current_id为最后一个请求的id + 1
    current_id = requests_list[-1]['id'] + 1
    try:
        response = requests.post(rpc_url, auth=(rpc_user, rpc_password), data=payload)
        response_data = response.json()
        # 检查是否为列表响应
        if isinstance(response_data, list):
            return response_data
        else:
            print(f"Error: {response_data}")  # 打印服务器返回的错误信息
            return None  # 或者返回一个默认值
    except requests.exceptions.ConnectionError as e:
        print(f"Connection error occurred: {e}")
        return None  # 返回 None 并继续执行
    except requests.RequestException as e:
        print(f"Request error: {e}, Response content: {response.content}")
        return None  # 或者返回一个默认值

    except json.JSONDecodeError:
        print(f"JSON decode error. Response content: {response.content}")
        return None  # 或者返回一个默认值
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None  # 处理其他未预期的异常


# 获取当前内存池中的所有交易
def get_mempool_transactions():
    query = [
        {
            "id": current_id,
            "jsonrpc": "2.0",
            "method": "getrawmempool",
            "params": []
        }]
    responses = rpc_batch(query)
    for i, resp in enumerate(responses):
        tx_data = resp['result']
        return set(tx_data)

# 批量查询交易详情
def get_transaction_details_batch(txids, batch_size=5000):
    tx_details = []
    for i in range(0, len(txids), batch_size):
        batch_txids = txids[i:i+batch_size]
        batch_query = [
            {
                "id": current_id + j,
                "jsonrpc": "2.0",
                "method": "getmempoolentry",
                "params": [txid]
            }
            for j, txid in enumerate(batch_txids)
        ]
        responses = rpc_batch(batch_query)
        for j, resp in enumerate(responses):
            if 'result' in resp:
                tx_data = resp['result']
                if tx_data is not None:  # 添加这个检查
                    fee_rate = calculate_fee_rate(tx_data)
                    tx_details.append({
                        'txid': batch_txids[j],
                        'fee': fee_rate,
                        'time': tx_data['time'],
                        'ancestorcount': tx_data['ancestorcount'],
                        'descendantcount': tx_data['descendantcount']
                    })
                else:
                    print(f"Warning: Transaction {batch_txids[j]} has no details.") #交易有可能已经被替换失效了
            else:
                print(f"Error fetching transaction details: {resp['error']}")
    return tx_details

def calculate_fee_rate(tx_data):
    total_fee = tx_data['fees']['ancestor'] + tx_data['fees']['descendant'] - tx_data['fees']['modified']
    total_size = tx_data['ancestorsize'] + tx_data['descendantsize'] - tx_data['vsize']
    #print(total_fee,total_size)
    return round(total_fee / total_size * 100000000, 2)

def get_mempool_ancestors(txids, batch_size=5000):
    all_ancestors = []
    for i in range(0, len(txids), batch_size):
        batch_txids = txids[i:i+batch_size]
        batch_query = [
            {
                "id": current_id + j,
                "jsonrpc": "2.0",
                "method": "getmempoolancestors",
                "params": [txid, True]
            }
            for j, txid in enumerate(batch_txids)
        ]
        responses = rpc_batch(batch_query)
        for resp in responses:
            if 'result' in resp:
                ancestors = resp['result']
                if ancestors is not None:
                    for ancestor_txid, ancestor_data in ancestors.items():
                        fee_rate = calculate_fee_rate(ancestor_data)
                        all_ancestors.append({
                            'txid': ancestor_txid,
                            'fee': fee_rate,
                            'time': ancestor_data['time']
                        })
                else:
                    print(f"Transaction {resp['id']} has no ancestors.")
            else:
                print(f"Error fetching mempool ancestors: {resp['error']}")
    
    return all_ancestors


# 定义操作码
OP_PUSHDATA1 = 0x4c
OP_PUSHDATA2 = 0x4d
OP_PUSHDATA4 = 0x4e
OP_IF = 0x63
OP_ENDIF = 0x68
PROTOCOL_ID = b'ord'

# 定义 OP_PUSHBYTES_X 系列操作码
OP_PUSHBYTES_1 = 0x01
OP_PUSHBYTES_8 = 0x8
# ...
OP_PUSHBYTES_75 = 0x4b
JUMP = b'\x01\x01'  # 定义要跳过的字节序列 PUSHBYTES_1 01
# 假设这些是Tag对应的字节表示，根据实际情况调整
TAG_MAP = {
    b'\x02': 'Pointer',
    b'\x66': 'Unbound',
    b'\x01': 'ContentType',
    b'\x03': 'Parent',
    b'\x05': 'Metadata',
    b'\x07': 'Metaprotocol',
    b'\x09': 'ContentEncoding',
    b'\x11': 'Delegate',
    b'\x255': 'Nop',
}

def get_data_length(hex_bytes, index, opcode):
    """Get the length of the data to be pushed onto the stack."""
    if opcode == OP_PUSHDATA1:
        return hex_bytes[index], index + 1
    elif opcode == OP_PUSHDATA2:
        return int.from_bytes(hex_bytes[index:index+2], byteorder='little'), index + 2
    elif opcode == OP_PUSHDATA4:
        return int.from_bytes(hex_bytes[index:index+4], byteorder='little'), index + 4
    else:
        return None, index
    
def analyze_inscriptions(hex,txid):
    inscriptions = []
    index = 0
    index_id = 0
    media_type = ""
    Metaprotocol = None
    tag = 0
    hex_bytes = bytes.fromhex(hex)

    while index < len(hex_bytes):
        # 查找 PROTOCOL_ID
        try:
            start = hex_bytes.index(PROTOCOL_ID, index)
            index = start + len(PROTOCOL_ID)
        except ValueError:
            break  # 没有找到 PROTOCOL_ID，结束解析
        # 尝试查找要跳过的字节序列 JUMP
        try:
            start = hex_bytes.index(JUMP, index)
            index = start + len(JUMP)  # 跳过 JUMP 字节序列
            #print(f"跳过 JUMP 字节序列，新位置: {index}")
        except ValueError:
            #print(f"未找到 JUMP 字节序列，txid:{txid}")
            index += 1
        if hex_bytes[index] == 1:   #这里这样处理是因为可能遇到批量的后面部分是这种形式的OP_PUSHBYTES_3 b0cc01 OP_PUSHBYTES_1 01,即定位到的JUMP少跳了一位,需要再补上
            index += 1
        # 基于标签解析媒体类型
        if OP_PUSHBYTES_1 <= hex_bytes[index] <= OP_PUSHBYTES_75:
            type_len = hex_bytes[index]
            index += 1  # 移过媒体类型长度字节
            media_type = hex_bytes[index:index + type_len].decode('utf-8', errors='ignore')
            index += type_len  # 移过媒体类型
        else:
            print(f"Unexpected data format at index {index}: expected media type. txid:{txid}")
            break
        
        tag_index = 0
        while index < len(hex_bytes) and OP_PUSHBYTES_1 <= hex_bytes[index] <= OP_PUSHBYTES_75:
            OP_PUSH_len = hex_bytes[index]
            #print(OP_PUSH_len)
            index += 1  # 移过OP_PUSH_len字节
            if tag_index == 0:    #首次未赋值
                tag = hex_bytes[index:index + OP_PUSH_len]
                tag_index = index + 1
            index += OP_PUSH_len  #移动到下个位置

        Metaprotocol = ""
        text_420 = ""

        if tag == b'\x07':
            OP_PUSH_X = hex_bytes[tag_index]
            tag_index += 1  # 移动到下个位置
            Metaprotocol = hex_bytes[tag_index:tag_index + OP_PUSH_X].decode('utf-8', errors='ignore')
            #print(f"Metaprotocol:{Metaprotocol}")
        elif tag == b'\x05':
            if hex_bytes[tag_index] in [OP_PUSHDATA1, OP_PUSHDATA2, OP_PUSHDATA4]:
                content_len, tag_index = get_data_length(hex_bytes, tag_index+1, hex_bytes[tag_index])
                Metadata = hex_bytes[tag_index:tag_index + content_len]
                index += content_len
                #Metadata = Metadata.decode('utf-8', errors='ignore')
                #print(f"Metadata:{Metadata}")

        if index_id >= 0:    #跳过其他铭文的部分索引信息
            while index < len(hex_bytes) and hex_bytes[index] != 0: #查找OP_0标志
                index += 1
            # while index < len(hex_bytes) and  hex_bytes[index] not in [OP_PUSHDATA1, OP_PUSHDATA2, OP_PUSHDATA4]:  #定位到后续是OP_PUSHDATA
            #     index += 1

        # 初始化内容
        content = b''
        content_len = 0
        # 解析铭文内容，直到遇到下一个 PROTOCOL_ID 或数据结束
        while index < len(hex_bytes) and hex_bytes[index] != OP_ENDIF:
            if hex_bytes[index] in [OP_PUSHDATA1, OP_PUSHDATA2, OP_PUSHDATA4]:
                content_len, index = get_data_length(hex_bytes, index+1, hex_bytes[index])
                content += hex_bytes[index:index + content_len]
                index += content_len
            else:
                content_len = hex_bytes[index]
                index += 1  #移动到数据
                content += hex_bytes[index:index + content_len]
                index += content_len
        
        if content_len == 75:
            text_420 = content.decode('utf-8', errors='ignore')
            if text_420.startswith("/content/"):
                #print(f"BRC-420:{media_type}    {text_420}")
                content = text_420
            else:
                text_420 = ""
        elif content_len == 0:  #解析不出来的跳过
            break
        
        if media_type.startswith("text/plain") or media_type.startswith("application/json") or text_420 != "":#这里是为了让420的写入数据库
            if text_420 == "":
                content = content.decode('utf-8', errors='ignore')
            #inscription_id = f"{txid}i{index_id}"
            #inscriptions.append({'id': inscription_id, 'type': media_type, 'content':content,'Metaprotocol':Metaprotocol})
            inscriptions.append({'index': index_id, 'type': media_type, 'content':content})
        else:
            pass  #其他类型的暂时不处理
            
        index_id += 1
    
    return inscriptions

def filter_inscriptions_transactions(mempool_txids, processed_txids, batch_size=5000):
    inscriptions_transactions = []
    
    new_txids = mempool_txids - processed_txids
    #print("new_txids数量:",len(new_txids))
    for i in range(0, len(new_txids), batch_size):
        batch_txids = list(new_txids)[i:i+batch_size]
        batch_query = [
            {
                "id": current_id + j,
                "jsonrpc": "2.0",
                "method": "getrawtransaction",
                "params": [txid, 1]  # 0 表示只返回交易的 hex 数据 # 2 表示返回详细的交易信息
            }
            for j, txid in enumerate(batch_txids)
        ]
        responses = rpc_batch(batch_query)

        for j, resp in enumerate(responses):
            try:
                if 'result' in resp:
                    tx = resp['result']
                    if tx is not None:
                        txid = tx['txid']
                        for vin in tx['vin']:
                            try:
                                txinwitness = vin.get('txinwitness', [])
                                for witness in txinwitness:
                                    try:
                                        inscriptions = analyze_inscriptions(witness, txid)
                                        if inscriptions:
                                            output_address = None
                                            for vout in tx['vout']:
                                                try:
                                                    if 'scriptPubKey' in vout and 'address' in vout['scriptPubKey']:
                                                        output_address = vout['scriptPubKey']['address']
                                                        break
                                                except Exception as e:
                                                    print(f"Error processing vout for txid {txid}: {str(e)}")
                                            
                                            data = {
                                                "txid": batch_txids[j],
                                                "inscriptions": inscriptions,
                                                "output_address": output_address
                                            }
                                            #print(data)
                                            inscriptions_transactions.append(data)
                                            processed_txids.add(batch_txids[j])
                                    except Exception as e:
                                        print(f"Error analyzing inscriptions for txid {txid}, witness {witness[:20]}...: {str(e)}")
                            except Exception as e:
                                print(f"Error processing vin for txid {txid}: {str(e)}")
                    else:
                        print(f"Warning: Transaction {batch_txids[j]} has no raw data.")
                else:
                    print(f"Error fetching transaction data: {resp.get('error', 'Unknown error')}")
            except Exception as e:
                print(f"Unexpected error processing response for txid {batch_txids[j]}: {str(e)}")
    
    return inscriptions_transactions, processed_txids


def load_inscriptions_transactions(filename):
    try:
        with open(filename, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def save_inscriptions_transactions(filename, transactions):
    with open(filename, 'w') as f:
        json.dump(transactions, f)

def process_transactions(new_txids, processed_txids,newScan=False):
    inscriptions_transactions = []
    if newScan == False: #首次初始化
        # 从文件中加载旧的符文交易
        old_inscriptions_transactions = load_inscriptions_transactions('old_inscriptions_transactions.json')
        # 提取旧交易中的 txid
        old_txids = set(tx['txid'] for tx in old_inscriptions_transactions)
        log_info(f"inscriptions:old_txids数量:{len(old_txids)}")
        # 过滤掉不在 new_txids 中的旧交易（这些交易可能已经失效）
        valid_old_txids = old_txids.intersection(new_txids)
        log_info(f"inscriptions:valid_old_txids数量:{len(valid_old_txids)}")
         # 找出需要查询的新交易
        filter_txids = new_txids - valid_old_txids
        log_info(f"inscriptions:filter_txids数量:{len(filter_txids)}")
        # 查询新的交易
        new_inscriptions_transactions, processed_txids = filter_inscriptions_transactions(filter_txids, processed_txids)
        log_info(f"inscriptions:new_inscriptions_transactions:{len(new_inscriptions_transactions)}")
        # 合并有效的旧交易和新查询的交易
        inscriptions_transactions = [tx for tx in old_inscriptions_transactions if tx['txid'] in valid_old_txids] + new_inscriptions_transactions
        log_info(f"inscriptions:inscriptions_transactions:{len(inscriptions_transactions)}")
        # 保存更新后的符文交易到文件
        save_inscriptions_transactions('old_inscriptions_transactions.json', inscriptions_transactions)
    else:
        inscriptions_transactions, processed_txids = filter_inscriptions_transactions(new_txids, processed_txids)

    #print('新增铭文数量:', len(inscriptions_transactions))
    inscriptions_txids = [tx["txid"] for tx in inscriptions_transactions]
    tx_details = get_transaction_details_batch(inscriptions_txids)  #这里返回的数据,交易已经计算过fee了
    if newScan:
        affected_txids = set()
        for tx in tx_details:
            if tx['ancestorcount'] > 1 and tx['descendantcount'] == 1:    #只获取最后一笔的进行修正,避免重复
                affected_txids.add(tx['txid'])
                #print(f"{tx['txid']} 需要更新关联fee")
        
        if affected_txids:
            updated_txs = get_mempool_ancestors(list(affected_txids))
            log_info(f"affected_txids数量:{len(affected_txids)},updated_txs数量:{len(updated_txs)}")

            #修正关联fee
            for tx in updated_txs:
                txid = tx['txid']
                if txid in inscriptions_cache:
                    # 如果交易存在于 inscriptions_cache 中,更新其费用
                    #old_fee = inscriptions_cache[txid]['fee']
                    new_fee = tx['fee']
                    inscriptions_cache[txid]['fee'] = new_fee
                    #print(f"更新 {txid} 的费率 {old_fee} -> {new_fee}")
        
    
    # 创建一个字典,保存每个交易的 inscriptions 和 output_address
    inscriptions_tx_dict = {tx['txid']: (tx['inscriptions'], tx['output_address']) for tx in inscriptions_transactions}
    
    for tx in tx_details:
        txid = tx['txid']
        fee = tx['fee']
        if txid in inscriptions_tx_dict:
            inscriptions, output_address = inscriptions_tx_dict[txid]
            inscriptions_cache[txid] = {
                "inscriptions": inscriptions,
                'fee': fee,
                'output_address': output_address
            }
    
    return tx_details, inscriptions_transactions, processed_txids

def cleanup_cache(mempool_txids):
    total_expired = 0
    expired_txids = set(inscriptions_cache.keys()) - mempool_txids
    
    for txid in expired_txids:
        del inscriptions_cache[txid]
        total_expired += 1
    
    if total_expired > 0:
        log_info(f"已从缓存中清理 {total_expired} 个过期交易。")

    return total_expired

def mempool_listener():
    global old_txids_general    #类型是set()
    # 第一次获取内存池交易并处理
    start_time = time.time()
    mempool_txids = get_mempool_transactions()
    processed_txids = set()
    log_info('初始内存池交易笔数:', len(mempool_txids))
    if len(old_txids_general):
         old_txids_general = old_txids_general.intersection(mempool_txids) #去除失效的id
         log_info('原保存文件有效txid数:', len(old_txids_general))

    #过滤掉已经处理过的交易,处理后old_txids_general里的数据应该是有效的数据了
    mempool_txids = mempool_txids - old_txids_general
    log_info('过滤完数据后交易笔数:', len(mempool_txids))
    

    tx_details, inscriptions_transactions, processed_txids = process_transactions(mempool_txids, processed_txids,False)

    # 处理初始的交易数据
    # 创建 tx_details 的字典索引
    tx_details_dict = {tx['txid']: tx for tx in tx_details}

    for tx in inscriptions_transactions:
        txid = tx['txid']
        inscriptions = tx['inscriptions']
        output_address = tx['output_address']
        
        # 直接从字典中获取交易信息
        tx_info = tx_details_dict.get(txid)
        
        if tx_info:
            fee = tx_info['fee']
            inscriptions_cache[txid] = {
                'inscriptions': inscriptions,
                'fee': fee,
                'output_address': output_address
            }

    # 这里筛选出mempool_txids与inscriptions_transactions里的txid差集,保存到new_txids_general
    new_txids_general = mempool_txids - set(tx['txid'] for tx in inscriptions_transactions)
    log_info('新的一般交易数量:', len(new_txids_general))
    filter_txids = old_txids_general.union(new_txids_general)  # 使用union()方法合并集合
    log_info('普通交易过滤集数量:', len(filter_txids))
    save_data_to_file('old_txids_general',filter_txids) #保存新的普通交易信息

    end_time = time.time()
    query_time = end_time - start_time
    log_info(f'铭文数据初始化完成,铭文数:{len(inscriptions_transactions)} time: {query_time:.4f} seconds')
    
    while not stop_signal.is_set():
        time.sleep(1)
        new_mempool_txids = get_mempool_transactions()
        new_txids = new_mempool_txids - mempool_txids - filter_txids    #新的查询减去上一轮的mempool_txids与过滤的txids
        
        if new_txids:
            try:
                # Process transactions and get details
                tx_details, inscriptions_transactions, processed_txids = process_transactions(new_txids, processed_txids, True)
                
                if inscriptions_transactions:
                    log_info("新铭文交易的详细信息：")
                    for idx, tx in enumerate(inscriptions_transactions, 1):
                        txid = tx['txid']
                        log_info(f"铭文交易 #{idx}:")
                        try:
                            fee = inscriptions_cache[txid]['fee']
                        except KeyError:
                            fee = "未知"
                        log_info(f"  交易ID: {txid}, fee: {fee}")
                        log_info(f"  输出地址: {tx['output_address']}")
                        log_info("  铭文内容:")
                        for inscription in tx['inscriptions']:
                            log_info(f"    - 类型: {inscription['type']}")
                            if 'content' in inscription:
                                content_preview = inscription['content'][:150] + '...' if len(inscription['content']) > 150 else inscription['content']
                                log_info(f"      内容预览: {content_preview}")
                        log_info("  " + "-"*40)  # Separator line
                mempool_txids = new_mempool_txids  # Update mempool_txids to the latest set of mempool transactions
                cleanup_cache(mempool_txids)  # Clean up expired transaction data in the cache
            except Exception as e:
                log_info(f"Error processing transactions: {str(e)}")

#调试用,用于查询某笔交易的父交易手续费
def get_txid_ancestors_fee(txid):
    all_ancestors = []
    responses = rpc_call('getmempoolancestors',[txid, True])
    ancestors = responses
    if ancestors is not None:
        for ancestor_txid, ancestor_data in ancestors.items():
            fee_rate = calculate_fee_rate(ancestor_data)
            all_ancestors.append({
                'txid': ancestor_txid,
                'fee': fee_rate,
                'time': ancestor_data['time']
            })
    
    return all_ancestors

#调试用,用于查询某笔交易的手续费
def get_txid_fee(txid):
    data = {}
    tx_data = rpc_call('getmempoolentry',[txid])
    if tx_data is not None:
        fee_rate = calculate_fee_rate(tx_data)
        data = {
            'txid': txid,
            'fee': fee_rate,
            'time': tx_data['time']
        }
    return data

if __name__ == "__main__":
    # 在启动时加载符文信息
    load_data_from_file()
    # 创建并启动内存池监听器线程
    listener_thread = threading.Thread(target=mempool_listener)
    listener_thread.start()
    # 启动 FastAPI 应用
    config = Config(app=app, host="0.0.0.0", port=5001)
    server = Server(config=config)
    server.run()