#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特定区块的扫描
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from batch_block_scanner import *

def test_specific_transaction():
    """测试特定交易"""
    print("=== 测试特定交易 ===")
    
    # 已知的交易哈希
    known_txid = "86349755fd5d89bc83a969449e56233543a74dfc93287aed3ff29f3d80d7a2d7"
    
    try:
        # 获取交易详细信息
        tx_data = rpc_call('getrawtransaction', [known_txid, True])
        if not tx_data:
            print(f"无法获取交易 {known_txid}")
            return
        
        print(f"交易ID: {tx_data.get('txid')}")
        print(f"区块哈希: {tx_data.get('blockhash', '未确认')}")
        
        # 检查witness条件
        has_valid_witness = False
        for vin in tx_data.get('vin', []):
            witness_list = vin.get('txinwitness', [])
            for witness in witness_list:
                if check_witness_conditions(witness):
                    has_valid_witness = True
                    print(f"✅ 找到符合条件的witness，长度: {len(witness)}")
                    break
            if has_valid_witness:
                break
        
        if not has_valid_witness:
            print("❌ 未找到符合条件的witness")
            return
        
        # 提取地址
        mint_address, receive_address, need_prevout = extract_addresses_from_transaction(tx_data)
        
        if need_prevout:
            print(f"需要查询前置交易: {need_prevout}")
            # 批量查询（虽然只有一个）
            input_address_map = get_input_addresses_batch([need_prevout])
            mint_address, receive_address, _ = extract_addresses_from_transaction(tx_data, input_address_map)
        
        print(f"铸造地址: {mint_address}")
        print(f"收货地址: {receive_address}")
        
        if mint_address and receive_address:
            print("✅ 成功提取所有地址信息")
        else:
            print("❌ 地址提取不完整")
            
    except Exception as e:
        print(f"测试失败: {e}")

def test_block_with_known_transaction():
    """测试包含已知交易的区块"""
    print("\n=== 测试包含已知交易的区块 ===")
    
    # 从已知交易获取区块高度
    known_txid = "86349755fd5d89bc83a969449e56233543a74dfc93287aed3ff29f3d80d7a2d7"
    
    try:
        # 获取交易信息
        tx_data = rpc_call('getrawtransaction', [known_txid, True])
        if not tx_data or 'blockhash' not in tx_data:
            print("无法获取交易的区块信息")
            return
        
        blockhash = tx_data['blockhash']
        
        # 获取区块信息
        block_data = rpc_call('getblock', [blockhash, 1])  # 只获取基本信息
        if not block_data:
            print("无法获取区块信息")
            return
        
        block_height = block_data['height']
        print(f"测试区块高度: {block_height}")
        print(f"区块哈希: {blockhash}")
        print(f"区块中交易数量: {len(block_data.get('tx', []))}")
        
        # 使用我们的脚本处理这个区块
        print("\n开始扫描区块...")
        records = process_block(block_height)
        
        print(f"扫描结果: 找到 {len(records)} 笔符合条件的交易")
        
        if records:
            print("\n找到的交易:")
            for i, record in enumerate(records, 1):
                print(f"  {i}. 交易ID: {record.txid}")
                print(f"     铸造地址: {record.mint_address}")
                print(f"     收货地址: {record.receive_address}")
                print(f"     区块高度: {record.block_height}")
                
                # 验证是否包含我们的已知交易
                if record.txid == known_txid:
                    print("     ✅ 这是我们的已知交易！")
        else:
            print("❌ 未找到符合条件的交易")
            
    except Exception as e:
        print(f"测试失败: {e}")

def main():
    """主函数"""
    print("特定区块扫描测试")
    print("=" * 50)
    
    # 测试特定交易
    test_specific_transaction()
    
    # 测试包含已知交易的区块
    test_block_with_known_transaction()
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    main()
