# 批量区块交易查询脚本使用说明

## 功能描述

`batch_block_scanner.py` 是一个基于现有 `labiebu.py` 脚本开发的批量查询历史区块交易的工具。

## 主要功能

- 批量扫描指定区块范围内的所有交易
- 筛选包含特定 witness 数据的交易
- 提取交易的关键信息（铸造地址、收货地址、交易哈希、区块编号）
- 生成 Excel 格式的结果文件

## 查询条件

脚本会查找同时满足以下条件的交易：

1. **包含 witness 字段**：交易必须包含 witness 数据
2. **witness 数据长度 > 1000 字符**：确保数据足够详细
3. **包含特定字符串**：witness 数据中必须包含字符串 `96053db5b18967b5a410326ecca687441579225a6d190f398e2180deec6e429e`

## 数据提取

从符合条件的交易中提取以下信息：

- **铸造地址**：从 `vin[0].prevout.scriptpubkey_address` 获取
- **收货地址**：从 `vout[0].scriptpubkey_address` 获取  
- **交易哈希**：从 `txid` 获取
- **区块编号**：当前处理的区块高度

## 安装依赖

在运行脚本前，请确保安装以下 Python 包：

```bash
pip install openpyxl tqdm requests
```

## 使用方法

1. **确保配置文件存在**：
   - 脚本会自动读取 `config.json` 配置文件
   - 如果不存在，会使用默认的比特币节点配置

2. **运行脚本**：
   ```bash
   python batch_block_scanner.py
   ```

3. **输入参数**：
   - 开始区块编号（整数）
   - 结束区块编号（整数）

4. **等待处理**：
   - 脚本会显示进度条
   - 实时显示当前处理的区块和找到的交易数量

5. **查看结果**：
   - 处理完成后会生成 Excel 文件
   - 文件名格式：`block_transactions_{开始区块}_{结束区块}_{时间戳}.xlsx`

## 输出格式

生成的 Excel 文件包含以下列：

| 列名 | 说明 |
|------|------|
| 编号 | 自增序号，从1开始 |
| 铸造地址 | 交易的铸造地址 |
| 收货地址 | 交易的收货地址 |
| 交易哈希 | 交易的唯一标识符 |
| 区块编号 | 交易所在的区块高度 |

## 示例输出

```
编号：1
铸造地址：bc1peve9pqv3k9hwfc57pu8mnjtdlyldp8jha57dgvqc3kexvrk7r43qlyqgsz
收货地址：bc1pxwu6adzrdp8y7g5a2ywsv9jwewdjnhpck367tcxrtjcmyf4a78hs5hfjzx
交易哈希：86349755fd5d89bc83a969449e56233543a74dfc93287aed3ff29f3d80d7a2d7
区块编号：908119
```

## 性能特点

- **批量处理**：使用高效的 RPC 批量调用
- **进度显示**：实时显示处理进度和统计信息
- **错误处理**：包含完善的错误处理和重试机制
- **内存优化**：按区块顺序处理，避免内存占用过大

## 注意事项

1. **区块范围限制**：
   - 单次查询建议不超过 10,000 个区块
   - 超过限制时会提示确认是否继续

2. **网络连接**：
   - 需要稳定的比特币节点 RPC 连接
   - 处理大量区块时可能需要较长时间

3. **文件权限**：
   - 确保脚本有权限在当前目录创建 Excel 文件

4. **中断处理**：
   - 支持 Ctrl+C 中断操作
   - 中断后已处理的数据不会保存

## 错误排查

如果遇到问题，请检查：

1. **配置文件**：确保 `config.json` 中的比特币节点配置正确
2. **网络连接**：确保能够连接到比特币节点
3. **依赖包**：确保所有必需的 Python 包已安装
4. **权限**：确保有文件写入权限

## 技术实现

脚本基于以下技术：

- **RPC 调用**：使用比特币节点的 JSON-RPC 接口
- **数据处理**：逐区块扫描和交易分析
- **Excel 生成**：使用 openpyxl 库生成格式化的 Excel 文件
- **进度显示**：使用 tqdm 库提供用户友好的进度条
