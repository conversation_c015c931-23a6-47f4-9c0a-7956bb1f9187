{"bitcoin_node": {"rpc_user": "__cookie__", "rpc_password": "deb7ba3f8be7ce034259adb1e1838140b31cd316709e6de00e6f550330050ff6", "rpc_host": "*************", "rpc_port": "8082"}, "brc20_api": {"url": "http://*************:8180", "path": "/v1/brc20/new_tick_info", "authorization": ""}, "server": {"host": "0.0.0.0", "port": 5002, "comment": "API server config: host is listen address, port is listen port"}, "logging": {"level": "WARNING", "comment": "Log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL. Use WARNING or ERROR for production"}, "performance": {"mempool_min_poll_interval": 10, "mempool_max_poll_interval": 30, "block_check_interval": 60, "batch_size": 2000, "initial_block_scan_count": 10, "max_cached_blocks": 20, "comment": "Performance tuning parameters"}}