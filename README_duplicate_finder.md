# 重复铸造地址查找工具使用说明

## 功能描述

`find_duplicate_addresses.py` 是一个用于查找Excel文件中重复铸造地址记录的工具。

## 主要功能

- 读取交易记录Excel文件
- 识别重复的铸造地址
- 生成包含所有重复记录的新Excel文件
- 提供详细的重复统计分析

## 使用方法

### 方法1：命令行指定文件
```bash
python find_duplicate_addresses.py 文件名.xlsx
```

### 方法2：交互式运行
```bash
python find_duplicate_addresses.py
```
然后按提示输入文件名，或直接回车使用当前目录下的交易文件。

## 输入文件格式

Excel文件应包含以下列（按顺序）：
1. 编号
2. 铸造地址
3. 收货地址  
4. 交易哈希
5. 区块编号

## 输出文件格式

生成的重复记录文件包含以下列：
1. 编号
2. 铸造地址
3. 收货地址
4. 交易哈希
5. 区块编号
6. 原文件行号
7. 重复组

## 输出示例

```
重复铸造地址查找工具
==================================================

正在读取文件: test_transactions_with_duplicates.xlsx
成功读取 7 条记录

=== 重复地址分析 ===
发现 2 个重复的铸造地址
地址 bc1peve9pqv3k9hwfc57... 有 3 条重复记录
  - 编号1: 区块908119, 交易86349755fd5d89bc...
  - 编号3: 区块908125, 交易1234567890abcdef...
  - 编号6: 区块908140, 交易3333333333333333...

统计信息:
  重复地址总数: 2
  重复记录总数: 5
  最多重复次数: 3

✅ 处理完成!
重复记录已保存到: xxx_duplicates_20250805_142327.xlsx
```

## 功能特点

1. **自动检测**：自动识别当前目录下的交易Excel文件
2. **详细分析**：提供重复地址的详细统计信息
3. **分组标记**：在输出文件中标记重复组，便于识别
4. **错误处理**：包含完善的错误处理和数据验证
5. **时间戳**：输出文件名包含时间戳，避免覆盖

## 注意事项

1. **文件格式**：确保输入的Excel文件格式正确
2. **数据完整性**：跳过数据不完整的行
3. **大小写敏感**：地址比较区分大小写
4. **内存使用**：大文件处理时注意内存使用

## 错误排查

如果遇到问题，请检查：

1. **文件存在**：确保指定的Excel文件存在
2. **文件格式**：确保文件是有效的Excel格式(.xlsx)
3. **列结构**：确保文件包含正确的列结构
4. **权限**：确保有读取输入文件和创建输出文件的权限

## 依赖包

```bash
pip install openpyxl
```

## 使用场景

- 数据清理：识别和处理重复的交易记录
- 数据验证：确保每个铸造地址只有一条记录
- 审计分析：分析重复交易的模式和分布
