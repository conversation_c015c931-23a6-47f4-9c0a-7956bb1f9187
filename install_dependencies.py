#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装批量区块扫描脚本的依赖包
"""
import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    """主函数"""
    print("批量区块扫描脚本 - 依赖安装工具")
    print("=" * 50)
    
    # 需要安装的包列表
    required_packages = [
        "openpyxl",
        "tqdm", 
        "requests"
    ]
    
    # 检查已安装的包
    print("检查已安装的包...")
    installed = []
    need_install = []
    
    for package in required_packages:
        if check_package(package):
            print(f"✅ {package} 已安装")
            installed.append(package)
        else:
            print(f"❌ {package} 未安装")
            need_install.append(package)
    
    if not need_install:
        print("\n🎉 所有依赖包都已安装！")
        return
    
    print(f"\n需要安装 {len(need_install)} 个包: {', '.join(need_install)}")
    
    # 询问是否继续安装
    response = input("\n是否继续安装？(y/n): ")
    if response.lower() != 'y':
        print("安装已取消")
        return
    
    # 安装缺失的包
    print("\n开始安装...")
    success_count = 0
    
    for package in need_install:
        if install_package(package):
            success_count += 1
    
    # 显示结果
    print("\n" + "=" * 50)
    print(f"安装完成: {success_count}/{len(need_install)} 个包安装成功")
    
    if success_count == len(need_install):
        print("🎉 所有依赖包安装成功！")
        print("\n现在可以运行批量区块扫描脚本了:")
        print("  python batch_block_scanner.py")
    else:
        print("⚠️  部分包安装失败，请手动安装失败的包")
        print("手动安装命令:")
        for package in need_install:
            print(f"  pip install {package}")

if __name__ == "__main__":
    main()
