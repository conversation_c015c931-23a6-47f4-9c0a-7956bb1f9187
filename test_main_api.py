#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主程序中的get_tick_info函数
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入主程序中的函数
from brc20 import get_tick_info, load_config

async def test_get_tick_info():
    """测试get_tick_info函数"""
    print("🧪 测试主程序中的get_tick_info函数")
    print("=" * 50)
    
    # 加载配置
    config = load_config()
    print(f"📋 当前API配置:")
    print(f"   URL: {config['brc20_api']['url']}")
    print(f"   Path: {config['brc20_api']['path']}")
    print(f"   Authorization: {config['brc20_api']['authorization']}")
    
    # 测试不同的tick
    test_ticks = ["jude", "ordi", "sats", "nonexistent"]
    
    for tick in test_ticks:
        print(f"\n{'='*20} 测试 {tick} {'='*20}")
        
        try:
            result = await get_tick_info(tick)
            
            if result:
                print(f"✅ {tick} 获取成功:")
                print(f"   Tick: {result.get('tick', 'N/A')}")
                print(f"   Supply: {result.get('supply', 'N/A')}")
                print(f"   Minted: {result.get('minted', 'N/A')}")
                print(f"   Decimal: {result.get('decimal', 'N/A')}")
                print(f"   Limit Per Mint: {result.get('limitPerMint', 'N/A')}")
                print(f"   Self Mint: {result.get('selfMint', 'N/A')}")
                print(f"   Deploy Height: {result.get('deployHeight', 'N/A')}")
                print(f"   Inscription ID: {result.get('inscriptionId', 'N/A')}")
            else:
                print(f"❌ {tick} 获取失败或不存在")
                
        except Exception as e:
            print(f"❌ {tick} 测试异常: {str(e)}")
        
        print("-" * 50)

async def main():
    """主函数"""
    print("🔧 主程序API函数测试工具")
    print("=" * 50)
    
    await test_get_tick_info()
    
    print("\n🎯 测试完成!")

if __name__ == "__main__":
    asyncio.run(main())
