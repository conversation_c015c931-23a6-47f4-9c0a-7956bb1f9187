# -*- coding: utf-8 -*-
import time
import requests
import json
import threading
import logging
import os
import re
import json5
import httpx

from fastapi import FastAPI, Path, Query, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from pydantic import BaseModel
from contextlib import asynccontextmanager
from typing import Union, List, Optional
import numpy as np
import math
from uvicorn.config import Config
from uvicorn.main import Server

# 加载配置文件
def load_config():
    try:
        config_path = os.path.join(os.path.dirname(__file__), 'config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 如果配置文件不存在,使用默认配置并创建配置文件
            default_config = {
                "bitcoin_node": {
                    "rpc_user": "__cookie__",
                    "rpc_password": "9083adb4113eb7ddc9acf665adf8a7a433c891844b1465c7c3cd9419982f1ad7",
                    "rpc_host": "*************",
                    "rpc_port": "8082"
                },
                "brc20_api": {
                    "url": "https://open-api.unisat.io",
                    "path": "/v1/indexer/brc20",
                    "authorization": "Bearer 81ca36b46819b911e5447f7853c78f01df26170c98fb31f4e78595c162049975"
                },
                "server": {
                    "host": "0.0.0.0",
                    "port": 5001,
                    "comment": "API服务器配置，host为监听地址，port为监听端口"
                },
                "logging": {
                    "level": "WARNING",
                    "comment": "日志等级: DEBUG, INFO, WARNING, ERROR, CRITICAL. 生产环境建议使用WARNING或ERROR"
                }
            }
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=4)
            return default_config
    except Exception as e:
        logging.error(f"加载配置文件失败: {str(e)}")
        raise

# 加载配置
config = load_config()

# 连接比特币节点
rpc_user = config['bitcoin_node']['rpc_user']
rpc_password = config['bitcoin_node']['rpc_password']
rpc_host = config['bitcoin_node']['rpc_host']
rpc_port = config['bitcoin_node']['rpc_port']
rpc_url = f'http://{rpc_host}:{rpc_port}'

# 连接BRC20 API
brc20_api_url = config['brc20_api']['url']
brc20_api_path = config['brc20_api']['path']
brc20_api_auth = config['brc20_api']['authorization']

# 性能配置
performance_config = config.get('performance', {})
MEMPOOL_MIN_POLL_INTERVAL = performance_config.get('mempool_min_poll_interval', 3)
MEMPOOL_MAX_POLL_INTERVAL = performance_config.get('mempool_max_poll_interval', 30)
BLOCK_CHECK_INTERVAL = performance_config.get('block_check_interval', 60)
BATCH_SIZE = performance_config.get('batch_size', 100)
INITIAL_BLOCK_SCAN_COUNT = performance_config.get('initial_block_scan_count', 10)
MAX_CACHED_BLOCKS = performance_config.get('max_cached_blocks', 20)

current_id = 1
stop_signal = threading.Event()

# 初始化线程变量
listener_thread = None
block_scanner_thread = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    # 启动时执行
    log_info("应用程序启动")
    
    # 快速启动，只加载必要的配置
    log_info("正在后台加载缓存和数据...")
    
    # 在后台线程中加载数据，不阻塞启动
    def background_init():
        try:
            load_tick_cache()
            load_data_from_file()
            load_block_cache()  # 加载区块缓存
            log_info("缓存和数据加载完成")
        except Exception as e:
            log_error(f"后台加载数据失败: {str(e)}")
    
    # 启动后台初始化
    bg_thread = threading.Thread(target=background_init, daemon=True)
    bg_thread.start()
    
    # 异步初始化区块缓存
    initialize_recent_blocks()
    
    # 创建并启动内存池监听器线程
    global listener_thread, block_scanner_thread
    try:
        listener_thread = threading.Thread(target=mempool_listener, daemon=True)
        listener_thread.start()
        log_info("内存池监听线程启动成功")
    except Exception as e:
        log_error(f"启动内存池监听线程失败: {str(e)}")
    
    # 创建并启动区块扫描监听线程  
    try:
        block_scanner_thread = threading.Thread(target=scan_recent_brc20_blocks, daemon=True)
        block_scanner_thread.start()
        log_info("区块扫描监听线程启动成功")
    except Exception as e:
        log_error(f"启动区块扫描监听线程失败: {str(e)}")
    
    log_info("应用程序快速启动完成，正在后台初始化数据...")
    yield
    
    # 关闭时执行
    log_info("准备结束进程")
    save_tick_cache()
    save_block_cache()  # 保存区块缓存
    stop_signal.set()
    
    # 等待线程结束
    try:
        if listener_thread and listener_thread.is_alive():
            listener_thread.join(timeout=10)
    except:
        pass
    
    try:
        if block_scanner_thread and block_scanner_thread.is_alive():
            block_scanner_thread.join(timeout=10)
    except:
        pass
    
    log_info("应用程序关闭")

app = FastAPI(lifespan=lifespan)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加GZip中间件
app.add_middleware(
    GZipMiddleware,
    minimum_size=1000  # 只压缩大于1KB的响应
)

inscriptions_cache = {}

# 非铭文ids
old_txids_general = set()

# 添加一个锁用于保护inscriptions_cache
inscriptions_cache_lock = threading.Lock()

# 添加区块缓存机制（类似 main.py）
block_cache = {}  # 区块缓存：{block_height: BRC20BlockInfo}（直接缓存统计结果）
scanned_blocks = set()  # 记录已扫描过的区块
MAX_RECENT_BLOCKS = MAX_CACHED_BLOCKS  # 使用配置文件中的值
recent_blocks_limit = 10  # 默认扫描最近10个区块

class InscriptionResult(BaseModel):
    txid: str
    type: str
    content: str
    output_address: str
    fee: float

class SearchResponse(BaseModel):
    total: int
    results: List[InscriptionResult]

class BRC20Result(BaseModel):
    txid: str
    tick: str
    operation: str
    amount: str
    fee: float
    output_address: str

class BRC20Response(BaseModel):
    total: int
    results: List[BRC20Result]

# 添加新的数据模型
class BRC20TrendingItem(BaseModel):
    tick: str
    total_txs: int
    total_amount: str
    unique_addresses: int
    median_fee: float
    inscriptionId: Optional[str]
    inscriptionNumber: Optional[int]
    supply: Optional[str]
    supply_raw: Optional[str]
    selfMint: Optional[bool]
    limitPerMint: Optional[str]
    minted: Optional[str]
    decimal: Optional[int]
    remaining_mint: Optional[str]
    remaining_mint_count: Optional[int]

class BRC20TrendingResponse(BaseModel):
    total: int
    results: List[BRC20TrendingItem]

# 添加新的数据模型
class TxidRankResponse(BaseModel):
    txid: str
    tick: str
    rank: int
    total: int
    fee: float

# 配置日志记录器
def setup_logging():
    """设置日志配置"""
    try:
        log_level_str = config.get('logging', {}).get('level', 'INFO').upper()
        log_level = getattr(logging, log_level_str, logging.INFO)
    except:
        log_level = logging.INFO
    
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

# 设置日志
setup_logging()

# 封装一个日志打印函数
def log_debug(*args):
    message = ' '.join(str(arg) for arg in args)
    logging.debug(message)

def log_info(*args):
    message = ' '.join(str(arg) for arg in args)
    logging.info(message)

def log_warning(*args):
    message = ' '.join(str(arg) for arg in args)
    logging.warning(message)

def log_error(*args):
    message = ' '.join(str(arg) for arg in args)
    logging.error(message)



def shutdown_save_data():
    log_info("准备提取交易信息进行保存")
    # 从 inscriptions_cache 提取符文信息
    inscriptions_transactions = []
    for content, txs in inscriptions_cache.items():
        for txid, tx_info in txs.items():
            inscriptions_transactions.append({
                "txid": txid,
                "content": content,
                "output_address": tx_info['output_address']
            })

    # 保存符文交易信息
    save_inscriptions_transactions('old_inscriptions_transactions.json', inscriptions_transactions)
    log_info(f"保存了 {len(inscriptions_transactions)} 笔铭文交易")

    # 获取最新的内存池交易,由于rune_cache并没有进行最后一次获取,所以有一定概率会丢失部分没统计到的新增符文
    new_mempool_txids = get_mempool_transactions()

    # 分离出普通交易
    inscriptions_txids = set(tx['txid'] for tx in inscriptions_transactions)
    normal_txids = new_mempool_txids - inscriptions_txids

    # 保存普通交易信息
    save_data_to_file('old_txids_general', list(normal_txids))
    log_info(f"保存了 {len(normal_txids)} 笔普通交易")

def rpc_call(method,params=[]):
    query = [
        {
            "id": current_id,
            "jsonrpc": "2.0",
            "method": method,
            "params": params
        }]
    responses = rpc_batch(query)
    for i, resp in enumerate(responses):
        data = resp['result']
        return data
    
def getblockcount():
    blockHeight = rpc_call('getblockcount',)
    return blockHeight

def getblockhash(blockHeight):
    blockhash = rpc_call('getblockhash',[blockHeight])
    return blockhash

# 获取区块交易
def getblock(blockhash):
    data = rpc_call('getblock',[blockhash,2])
    return data

# 从文件加载信息到内存
def load_data_from_file():
    global old_txids_general
    if os.path.exists('old_txids_general.json'):
        with open('old_txids_general.json', 'r', encoding='utf-8') as f:
            old_txids_general = set(json.load(f))

# 保存数据到文件
def save_data_to_file(filename = None,data = None):
    if filename:
        with open(f'{filename}.json', 'w', encoding='utf-8') as f:
            json.dump(list(data), f, indent=2, ensure_ascii=False)
    else:
        pass

def generate_intervals(fees, max_intervals=8):
    """
    智能生成手续费区间，根据数据分布动态调整密集程度
    返回元组：(intervals, decimal_places)
    """
    import numpy as np
    
    unique_fees = sorted(set(fees))
    if len(unique_fees) == 1:
        return [(unique_fees[0], unique_fees[0] + 0.01)], 2
    
    min_fee, max_fee = unique_fees[0], unique_fees[-1]
    median_fee = np.median(fees)
    fee_range = max_fee - min_fee
    
    # 特殊处理：如果费率非常集中（范围小于0.5），使用精细区间
    if fee_range <= 0.5:
        # 对于集中分布，创建更细致的区间
        if fee_range <= 0.1:
            # 非常集中的分布，使用0.01为步长
            step = 0.01
        elif fee_range <= 0.2:
            # 较集中的分布，使用0.02为步长
            step = 0.02
        else:
            # 中等集中的分布，使用0.05为步长
            step = 0.05
        
        intervals = []
        current = min_fee
        while current < max_fee:
            next_point = min(current + step, max_fee + 0.01)
            # 检查这个区间是否有数据
            fees_in_range = [f for f in fees if current <= f < next_point]
            if fees_in_range:
                intervals.append((current, next_point))
            current = next_point
            
            # 限制区间数量
            if len(intervals) >= max_intervals:
                # 如果超过限制，合并最后一个区间
                if current < max_fee + 0.01:
                    intervals[-1] = (intervals[-1][0], max_fee + 0.01)
                break
        
        # 决定小数点位数
        decimal_places = 1 if len(intervals) >= 5 else 2
        return intervals, decimal_places
    
    # 如果唯一费用数量较少但分布不集中，直接为每个创建区间
    if len(unique_fees) <= max_intervals:
        intervals = []
        for i in range(len(unique_fees)):
            if i == len(unique_fees) - 1:
                intervals.append((unique_fees[i], unique_fees[i] + 0.01))
            else:
                intervals.append((unique_fees[i], unique_fees[i + 1]))
        # 决定小数点位数：区间数 >= 5 时用1位，否则用2位
        decimal_places = 1 if len(intervals) >= 5 else 2
        return intervals, decimal_places
    
    # 分析数据分布
    fees_below_1 = [f for f in fees if f < 1.0]
    fees_above_1 = [f for f in fees if f >= 1.0]
    
    has_high_fees = len(fees_above_1) > 0
    low_fee_ratio = len(fees_below_1) / len(fees)
    
    intervals = []
    
    if not has_high_fees:
        # 没有大于1gas的交易，专注于1以下的精细分布
        low_thresholds = [0.05, 0.1, 0.2, 0.3, 0.5, 0.7, 1.0]
        
        for i in range(len(low_thresholds) - 1):
            start_threshold = low_thresholds[i]
            end_threshold = low_thresholds[i + 1]
            
            fees_in_range = [f for f in fees if start_threshold <= f < end_threshold]
            if fees_in_range:
                intervals.append((start_threshold, end_threshold))
            
            # 限制到5个区间
            if len(intervals) >= 5:
                break
        
        # 如果还有剩余的费用，合并到最后一个区间
        if intervals and max_fee >= intervals[-1][1]:
            last_start = intervals[-1][0]
            intervals[-1] = (last_start, max_fee + 0.01)
    
    elif low_fee_ratio >= 0.7:
        # 大部分交易在1以下，但有少量高费用交易
        # 给1以下分配3-4个区间，1以上分配1-2个区间
        
        # 1以下的精细分布
        low_thresholds = [0.1, 0.2, 0.5, 1.0]
        for i in range(len(low_thresholds) - 1):
            start_threshold = low_thresholds[i]
            end_threshold = low_thresholds[i + 1]
            
            fees_in_range = [f for f in fees if start_threshold <= f < end_threshold]
            if fees_in_range:
                intervals.append((start_threshold, end_threshold))
        
        # 1以上的分布
        if median_fee < 2:
            # 中位数较低，简单分1-2个高费用区间
            high_thresholds = [1.0, 5.0, float('inf')]
        else:
            # 中位数较高，需要更多高费用区间
            high_thresholds = [1.0, 2.0, 5.0, float('inf')]
        
        for i in range(len(high_thresholds) - 1):
            start_threshold = high_thresholds[i]
            end_threshold = high_thresholds[i + 1]
            
            fees_in_range = [f for f in fees if start_threshold <= f < end_threshold]
            if fees_in_range:
                if end_threshold == float('inf'):
                    intervals.append((start_threshold, max_fee + 0.01))
                else:
                    intervals.append((start_threshold, end_threshold))
            
            # 控制总区间数
            if len(intervals) >= max_intervals:
                break
    
    else:
        # 高费用交易占比较大，需要均衡分布
        if median_fee >= 1:
            # 中位数在1以上，给高费用更多区间
            if median_fee >= 5:
                # 很高的中位数
                thresholds = [0.1, 0.5, 1.0, 2.0, 5.0, 10.0, 20.0, float('inf')]
            else:
                # 中等偏高的中位数
                thresholds = [0.1, 0.5, 1.0, 2.0, 5.0, 10.0, float('inf')]
        else:
            # 中位数在1以下但有很多高费用交易
            thresholds = [0.1, 0.2, 0.5, 1.0, 2.0, 5.0, 10.0, float('inf')]
        
        for i in range(len(thresholds) - 1):
            start_threshold = thresholds[i]
            end_threshold = thresholds[i + 1]
            
            fees_in_range = [f for f in fees if start_threshold <= f < end_threshold]
            if fees_in_range:
                if end_threshold == float('inf'):
                    intervals.append((start_threshold, max_fee + 0.01))
                else:
                    intervals.append((start_threshold, end_threshold))
            
            if len(intervals) >= max_intervals:
                break
    
    # 确保至少有一个区间
    if not intervals:
        intervals = [(min_fee, max_fee + 0.01)]
    
    # 处理边界情况：如果第一个区间的起始点大于最小费用
    if intervals and intervals[0][0] > min_fee:
        intervals[0] = (min_fee, intervals[0][1])
    
    # 决定小数点位数：区间数 >= 5 时用1位，否则用2位
    decimal_places = 1 if len(intervals) >= 5 else 2
    
    return intervals, decimal_places

@app.get("/api/search_inscriptions", response_model=SearchResponse)
async def search_inscriptions(
    content: str = Query(..., description="Content to search for in inscriptions"),
    type: Optional[str] = Query(None, description="Type of inscription to filter by")
):
    results = []
    # 复制字典内容到临时变量
    temp_cache = inscriptions_cache.copy()

    for txid, data in temp_cache.items():
        for inscription in data['inscriptions']:
            if type and inscription['type'] != type:
                continue
            if re.search(content, inscription.get('content', ''), re.IGNORECASE):
                results.append(InscriptionResult(
                    txid=txid,
                    type=inscription['type'],
                    content=inscription.get('content', ''),
                    output_address=data['output_address'],
                    fee=data['fee']
                ))

    if not results:
        raise HTTPException(status_code=404, detail="No matching inscriptions found")
    # 按照 fee 进行降序排序
    sorted_results = sorted(results, key=lambda x: x.fee, reverse=True)

    return SearchResponse(total=len(results), results=sorted_results)

def rpc_batch(requests_list):
    """
    执行批量RPC调用
    :param requests_list: 包含多个请求字典的列表
    :return: 响应列表
    """
    global current_id  # 声明使用全局变量 current_id

    payload = json.dumps(requests_list)

    # 更新current_id为最后一个请求的id + 1
    current_id = requests_list[-1]['id'] + 1
    try:
        response = requests.post(rpc_url, auth=(rpc_user, rpc_password), data=payload)
        response_data = response.json()
        # 检查是否为列表响应
        if isinstance(response_data, list):
            return response_data
        else:
            print(f"Error: {response_data}")  # 打印服务器返回的错误信息
            return None  # 或者返回一个默认值
    except requests.exceptions.ConnectionError as e:
        print(f"Connection error occurred: {e}")
        return None  # 返回 None 并继续执行
    except requests.RequestException as e:
        print(f"Request error: {e}, Response content: {response.content}")
        return None  # 或者返回一个默认值

    except json.JSONDecodeError:
        print(f"JSON decode error. Response content: {response.content}")
        return None  # 或者返回一个默认值
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        return None  # 处理其他未预期的异常


# 获取当前内存池中的所有交易
def get_mempool_transactions():
    query = [
        {
            "id": current_id,
            "jsonrpc": "2.0",
            "method": "getrawmempool",
            "params": []
        }]
    responses = rpc_batch(query)
    if responses is None:
        log_error("获取内存池交易失败：RPC调用返回None")
        return set()
    
    for i, resp in enumerate(responses):
        if 'result' in resp and resp['result'] is not None:
            tx_data = resp['result']
            return set(tx_data)
        else:
            log_error(f"获取内存池交易失败：{resp.get('error', '未知错误')}")
            return set()

# 批量查询交易情况
def get_transaction_details_batch(txids, batch_size=2000):
    tx_details = []
    for i in range(0, len(txids), batch_size):
        batch_txids = txids[i:i+batch_size]
        batch_query = [
            {
                "id": current_id + j,
                "jsonrpc": "2.0",
                "method": "getmempoolentry",
                "params": [txid]
            }
            for j, txid in enumerate(batch_txids)
        ]
        responses = rpc_batch(batch_query)
        for j, resp in enumerate(responses):
            if 'result' in resp:
                tx_data = resp['result']
                if tx_data is not None:  # 添加这个检查
                    fee_rate = calculate_fee_rate(tx_data)
                    tx_details.append({
                        'txid': batch_txids[j],
                        'fee': fee_rate,
                        'time': tx_data['time'],
                        'ancestorcount': tx_data['ancestorcount'],
                        'descendantcount': tx_data['descendantcount']
                    })
                else:
                    print(f"Warning: Transaction {batch_txids[j]} has no details.") #交易有可能已经被替换失效了
            else:
                print(f"Error fetching transaction details: {resp['error']}")
    return tx_details

def calculate_fee_rate(tx_data):
    total_fee = tx_data['fees']['ancestor'] + tx_data['fees']['descendant'] - tx_data['fees']['modified']
    total_size = tx_data['ancestorsize'] + tx_data['descendantsize'] - tx_data['vsize']
    #print(total_fee,total_size)
    return round(total_fee / total_size * 100000000, 2)

def get_mempool_ancestors(txids, batch_size=2000):
    all_ancestors = []
    for i in range(0, len(txids), batch_size):
        batch_txids = txids[i:i+batch_size]
        batch_query = [
            {
                "id": current_id + j,
                "jsonrpc": "2.0",
                "method": "getmempoolancestors",
                "params": [txid, True]
            }
            for j, txid in enumerate(batch_txids)
        ]
        responses = rpc_batch(batch_query)
        for resp in responses:
            if 'result' in resp:
                ancestors = resp['result']
                if ancestors is not None:
                    for ancestor_txid, ancestor_data in ancestors.items():
                        fee_rate = calculate_fee_rate(ancestor_data)
                        all_ancestors.append({
                            'txid': ancestor_txid,
                            'fee': fee_rate,
                            'time': ancestor_data['time']
                        })
                else:
                    print(f"Transaction {resp['id']} has no ancestors.")
            else:
                print(f"Error fetching mempool ancestors: {resp['error']}")
    
    return all_ancestors


# 定义操作码
OP_PUSHDATA1 = 0x4c
OP_PUSHDATA2 = 0x4d
OP_PUSHDATA4 = 0x4e
OP_IF = 0x63
OP_ENDIF = 0x68
PROTOCOL_ID = b'ord'

# 定义 OP_PUSHBYTES_X 系列操作码
OP_PUSHBYTES_1 = 0x01
OP_PUSHBYTES_8 = 0x8
# ...
OP_PUSHBYTES_75 = 0x4b
JUMP = b'\x01\x01'  # 定义要跳过的字节序列 PUSHBYTES_1 01
# 假设这些是Tag对应的字节表示，根据实际情况调整
TAG_MAP = {
    b'\x02': 'Pointer',
    b'\x66': 'Unbound',
    b'\x01': 'ContentType',
    b'\x03': 'Parent',
    b'\x05': 'Metadata',
    b'\x07': 'Metaprotocol',
    b'\x09': 'ContentEncoding',
    b'\x11': 'Delegate',
    b'\x255': 'Nop',
}

def get_data_length(hex_bytes, index, opcode):
    """Get the length of the data to be pushed onto the stack."""
    if opcode == OP_PUSHDATA1:
        return hex_bytes[index], index + 1
    elif opcode == OP_PUSHDATA2:
        return int.from_bytes(hex_bytes[index:index+2], byteorder='little'), index + 2
    elif opcode == OP_PUSHDATA4:
        return int.from_bytes(hex_bytes[index:index+4], byteorder='little'), index + 4
    else:
        return None, index
    
def analyze_inscriptions(hex,txid):
    inscriptions = []
    index = 0
    index_id = 0
    media_type = ""
    Metaprotocol = None
    tag = 0
    hex_bytes = bytes.fromhex(hex)

    while index < len(hex_bytes):
        # 查找 PROTOCOL_ID
        try:
            start = hex_bytes.index(PROTOCOL_ID, index)
            index = start + len(PROTOCOL_ID)
        except ValueError:
            break  # 没有找到 PROTOCOL_ID，结束解析
        # 尝试查找要跳过的字节序列 JUMP
        try:
            start = hex_bytes.index(JUMP, index)
            index = start + len(JUMP)  # 跳过 JUMP 字节序列
            #print(f"跳过 JUMP 字节序列，新位置: {index}")
        except ValueError:
            #print(f"未找到 JUMP 字节序列，txid:{txid}")
            index += 1
        if hex_bytes[index] == 1:   #这里这样处理是因为可能遇到批量的后面部是这种形式的OP_PUSHBYTES_3 b0cc01 OP_PUSHBYTES_1 01,即定位到的JUMP少跳了一位,需要再补上
            index += 1
        # 基于标签解析媒体类型
        if OP_PUSHBYTES_1 <= hex_bytes[index] <= OP_PUSHBYTES_75:
            type_len = hex_bytes[index]
            index += 1  # 移过媒体类型长度字节
            media_type = hex_bytes[index:index + type_len].decode('utf-8', errors='ignore')
            index += type_len  # 移过媒体类型
        else:
            log_debug(f"Unexpected data format at index {index}: expected media type. txid:{txid}")
            break
        
        tag_index = 0
        while index < len(hex_bytes) and OP_PUSHBYTES_1 <= hex_bytes[index] <= OP_PUSHBYTES_75:
            OP_PUSH_len = hex_bytes[index]
            #print(OP_PUSH_len)
            index += 1  # 移过OP_PUSH_len字节
            if tag_index == 0:    #首未赋值
                tag = hex_bytes[index:index + OP_PUSH_len]
                tag_index = index + 1
            index += OP_PUSH_len  #移动到下个位置

        Metaprotocol = ""
        text_420 = ""

        if tag == b'\x07':
            OP_PUSH_X = hex_bytes[tag_index]
            tag_index += 1  # 移动到下个位置
            Metaprotocol = hex_bytes[tag_index:tag_index + OP_PUSH_X].decode('utf-8', errors='ignore')
            #print(f"Metaprotocol:{Metaprotocol}")
        elif tag == b'\x05':
            if hex_bytes[tag_index] in [OP_PUSHDATA1, OP_PUSHDATA2, OP_PUSHDATA4]:
                content_len, tag_index = get_data_length(hex_bytes, tag_index+1, hex_bytes[tag_index])
                Metadata = hex_bytes[tag_index:tag_index + content_len]
                index += content_len
                #Metadata = Metadata.decode('utf-8', errors='ignore')
                #print(f"Metadata:{Metadata}")

        if index_id >= 0:    #跳过其他铭文的部分索引信息
            while index < len(hex_bytes) and hex_bytes[index] != 0: #查找OP_0标志
                index += 1
            # while index < len(hex_bytes) and  hex_bytes[index] not in [OP_PUSHDATA1, OP_PUSHDATA2, OP_PUSHDATA4]:  #定位后续是OP_PUSHDATA
            #     index += 1

        # 初始化内容
        content = b''
        content_len = 0
        # 解析铭文内容，直到遇到下一个 PROTOCOL_ID 或数结束
        while index < len(hex_bytes) and hex_bytes[index] != OP_ENDIF:
            if hex_bytes[index] in [OP_PUSHDATA1, OP_PUSHDATA2, OP_PUSHDATA4]:
                content_len, index = get_data_length(hex_bytes, index+1, hex_bytes[index])
                content += hex_bytes[index:index + content_len]
                index += content_len
            else:
                content_len = hex_bytes[index]
                index += 1  #移动到数据
                content += hex_bytes[index:index + content_len]
                index += content_len
        
        if content_len == 75:
            text_420 = content.decode('utf-8', errors='ignore')
            if text_420.startswith("/content/"):
                #print(f"BRC-420:{media_type}    {text_420}")
                content = text_420
            else:
                text_420 = ""
        elif content_len == 0:  #解析不出来的跳过
            break
        
        if media_type.startswith("text/plain") or media_type.startswith("application/json") or text_420 != "":#这里是为了让420的写入数据库
            if text_420 == "":
                content = content.decode('utf-8', errors='ignore')
            #inscription_id = f"{txid}i{index_id}"
            #inscriptions.append({'id': inscription_id, 'type': media_type, 'content':content,'Metaprotocol':Metaprotocol})
            inscriptions.append({'index': index_id, 'type': media_type, 'content':content})
        else:
            pass  #其他类型的暂时不处理
            
        index_id += 1
    
    return inscriptions

def filter_inscriptions_transactions(mempool_txids, processed_txids, batch_size=2000):
    inscriptions_transactions = []
    
    new_txids = mempool_txids - processed_txids
    #print("new_txids数量:",len(new_txids))
    for i in range(0, len(new_txids), batch_size):
        batch_txids = list(new_txids)[i:i+batch_size]
        batch_query = [
            {
                "id": current_id + j,
                "jsonrpc": "2.0",
                "method": "getrawtransaction",
                "params": [txid, 1]  # 0 表示只返回交易的 hex 数据 # 2 表示返回详细的交易信息
            }
            for j, txid in enumerate(batch_txids)
        ]
        responses = rpc_batch(batch_query)

        for j, resp in enumerate(responses):
            try:
                if 'result' in resp:
                    tx = resp['result']
                    if tx is not None:
                        txid = tx['txid']
                        for vin in tx['vin']:
                            try:
                                txinwitness = vin.get('txinwitness', [])
                                for witness in txinwitness:
                                    try:
                                        inscriptions = analyze_inscriptions(witness, txid)
                                        if inscriptions:
                                            output_address = None
                                            for vout in tx['vout']:
                                                try:
                                                    if 'scriptPubKey' in vout and 'address' in vout['scriptPubKey']:
                                                        output_address = vout['scriptPubKey']['address']
                                                        break
                                                except Exception as e:
                                                    log_debug(f"Error processing vout for txid {txid}: {str(e)}")
                                            
                                            data = {
                                                "txid": batch_txids[j],
                                                "inscriptions": inscriptions,
                                                "output_address": output_address
                                            }
                                            inscriptions_transactions.append(data)
                                            processed_txids.add(batch_txids[j])
                                    except Exception as e:
                                        log_debug(f"Error analyzing inscriptions for txid {txid}, witness {witness[:20]}...: {str(e)}")
                            except Exception as e:
                                log_debug(f"Error processing vin for txid {txid}: {str(e)}")
                    else:
                        log_debug(f"Transaction {batch_txids[j]} has no raw data (已确认或不存在)")
                else:
                    log_warning(f"Error fetching transaction data: {resp.get('error', 'Unknown error')}")
            except Exception as e:
                log_debug(f"Unexpected error processing response for txid {batch_txids[j]}: {str(e)}")
    
    return inscriptions_transactions, processed_txids


def load_inscriptions_transactions(filename):
    try:
        with open(filename, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def save_inscriptions_transactions(filename, transactions):
    with open(filename, 'w') as f:
        json.dump(transactions, f)

def parse_brc20_inscription(content: str) -> Optional[dict]:
    """
    解析BRC20铭文内容,验证是否为有效的BRC20铭文
    返回None表示无效,返回字典表示有效
    """
    try:
        # 预处理内容，只去除首尾的换行和制表符，保留空格
        content = content.strip('\n\t\r')
        
        # 检查是否以 { 开头和 } 结尾
        if not (content.startswith('{') and content.endswith('}')):
            return None
            
        # 尝试解析JSON内容
        try:
            data = json5.loads(content)
        except Exception:
            # 如果解析失败,尝试移除可能的Unicode字符和特殊字符，但保留空格
            cleaned_content = ''.join(c for c in content if c.isprintable() or c.isspace())
            try:
                data = json5.loads(cleaned_content)
            except Exception:
                return None
        
        # 验证必要字段
        if not isinstance(data, dict):
            return None
            
        # 验证协议和操作，这里可以strip()因为协议名和操作名不应该包含空格
        protocol = str(data.get('p', '')).strip().lower()
        if protocol != 'brc-20':
            return None
            
        operation = str(data.get('op', '')).strip().lower()
        if operation not in ['mint', 'deploy', 'transfer']:
            return None
            
        # 获取tick值，保持原样不做任何处理
        tick = str(data.get('tick', ''))
        if not tick:  # 只检查是否为空
            return None
            
        # 获取amount值
        amount = str(data.get('amt', '0')).strip()
        
        # 构建返回数据
        result = {
            'tick': tick,  # 完全保持原始tick
            'operation': operation,
            'amount': amount
        }
        
        return result
        
    except Exception as e:
        content_preview = content[:50] + '...' if len(content) > 50 else content
        log_error(f"解析BRC20内容时出错: {str(e)}, 内容预览: {content_preview}")
        return None

def process_transactions(new_txids, processed_txids, newScan=False):
    inscriptions_transactions = []
    if newScan == False: #首次初始化
        # 从文件中加载旧的符文交易
        old_inscriptions_transactions = load_inscriptions_transactions('old_inscriptions_transactions.json')
        # 提取旧交易中的 txid
        old_txids = set(tx['txid'] for tx in old_inscriptions_transactions)
        log_debug(f"inscriptions:old_txids数量:{len(old_txids)}")
        # 过滤掉不在 new_txids 中的旧交易（这些交易可能已经失效）
        valid_old_txids = old_txids.intersection(new_txids)
        log_debug(f"inscriptions:valid_old_txids数量:{len(valid_old_txids)}")
         # 找出需要查询的新交易
        filter_txids = new_txids - valid_old_txids
        log_debug(f"inscriptions:filter_txids数量:{len(filter_txids)}")
        # 查询新的交易
        new_inscriptions_transactions, processed_txids = filter_inscriptions_transactions(filter_txids, processed_txids)
        log_info(f"inscriptions:new_inscriptions_transactions:{len(new_inscriptions_transactions)}")
        # 合并有效的旧交易新查询的交易
        inscriptions_transactions = [tx for tx in old_inscriptions_transactions if tx['txid'] in valid_old_txids] + new_inscriptions_transactions
        log_info(f"inscriptions:inscriptions_transactions:{len(inscriptions_transactions)}")
        # 保存更新后的符文交易到文件
        save_inscriptions_transactions('old_inscriptions_transactions.json', inscriptions_transactions)
    else:
        inscriptions_transactions, processed_txids = filter_inscriptions_transactions(new_txids, processed_txids)

    #print('新增铭文数量:', len(inscriptions_transactions))
    inscriptions_txids = [tx["txid"] for tx in inscriptions_transactions]
    tx_details = get_transaction_details_batch(inscriptions_txids)  #这里返回的数据,交易已经计算过fee了
    if newScan:
        affected_txids = set()
        for tx in tx_details:
            if tx['ancestorcount'] > 1 and tx['descendantcount'] == 1:    #只获取最后一笔的进行修正,避免重复
                affected_txids.add(tx['txid'])
                #print(f"{tx['txid']} 需要更新关联fee")
        
        if affected_txids:
            updated_txs = get_mempool_ancestors(list(affected_txids))
            log_debug(f"affected_txids数量:{len(affected_txids)},updated_txs数量:{len(updated_txs)}")

            #正关联fee
            for tx in updated_txs:
                txid = tx['txid']
                if txid in inscriptions_cache:
                    # 如果交存在于 inscriptions_cache 中,更新其费用
                    #old_fee = inscriptions_cache[txid]['fee']
                    new_fee = tx['fee']
                    inscriptions_cache[txid]['fee'] = new_fee
                    #print(f"更新 {txid} 的费率 {old_fee} -> {new_fee}")
        
    
    # 创建一个字典,保存每个交易的 inscriptions 和 output_address
    inscriptions_tx_dict = {tx['txid']: (tx['inscriptions'], tx['output_address']) for tx in inscriptions_transactions}
    
    for tx in tx_details:
        txid = tx['txid']
        fee = tx['fee']
        if txid in inscriptions_tx_dict:
            inscriptions, output_address = inscriptions_tx_dict[txid]
            inscriptions_cache[txid] = {
                "inscriptions": inscriptions,
                'fee': fee,
                'output_address': output_address
            }
    
    for tx in inscriptions_transactions:
        txid = tx['txid']
        inscriptions = tx['inscriptions']
        output_address = tx['output_address']
        
        # 处理每个铭文
        for inscription in inscriptions:
            # 检查类型是否符合BRC20
            if inscription['type'] in ['text/plain;charset=utf-8', 'text/plain']:
                # 解析BRC20内容
                brc20_data = parse_brc20_inscription(inscription['content'])
                if brc20_data:
                    # 将BRC20信息添加到inscription中
                    inscription['brc20'] = brc20_data
        
    return tx_details, inscriptions_transactions, processed_txids

def cleanup_cache(mempool_txids):
    total_expired = 0
    expired_txids = set(inscriptions_cache.keys()) - mempool_txids
    
    for txid in expired_txids:
        del inscriptions_cache[txid]
        total_expired += 1
    
    if total_expired > 0:
        log_debug(f"已从缓存中清理 {total_expired} 个过期交易。")

    return total_expired

def mempool_listener():
    global old_txids_general    #类型是set()
    # 第一次获取内存池交易并处理
    start_time = time.time()
    mempool_txids = get_mempool_transactions()
    processed_txids = set()
    log_info('初始内存池交易笔数:', len(mempool_txids))
    if len(old_txids_general):
         old_txids_general = old_txids_general.intersection(mempool_txids) #去除失效的id
         log_debug('原保存文件有效txid数:', len(old_txids_general))

    #过滤掉已经处理过的交易,处理后old_txids_general里的数据应该是有效的数据了
    mempool_txids = mempool_txids - old_txids_general
    log_debug('过滤完数据后交易笔数:', len(mempool_txids))
    

    tx_details, inscriptions_transactions, processed_txids = process_transactions(mempool_txids, processed_txids,False)

    # 处理初始的交易数据
    # 创建 tx_details 的字典索引
    tx_details_dict = {tx['txid']: tx for tx in tx_details}

    for tx in inscriptions_transactions:
        txid = tx['txid']
        inscriptions = tx['inscriptions']
        output_address = tx['output_address']
        
        # 直接从字典中获取交易信息
        tx_info = tx_details_dict.get(txid)
        
        if tx_info:
            fee = tx_info['fee']
            inscriptions_cache[txid] = {
                'inscriptions': inscriptions,
                'fee': fee,
                'output_address': output_address
            }

    # 这里筛选出mempool_txids与inscriptions_transactions里的txid差集,保存到new_txids_general
    new_txids_general = mempool_txids - set(tx['txid'] for tx in inscriptions_transactions)
    log_debug('新的一般交易数量:', len(new_txids_general))
    filter_txids = old_txids_general.union(new_txids_general)  # 使用union()方法合并集合
    log_debug('普通交易过滤集数量:', len(filter_txids))
    save_data_to_file('old_txids_general',filter_txids) #保存新的普通交易信息

    end_time = time.time()
    query_time = end_time - start_time
    log_info(f'铭文数据初始化完成,铭文数:{len(inscriptions_transactions)} time: {query_time:.4f} seconds')
    
    # 自适应轮询变量
    consecutive_empty_polls = 0  # 连续空轮询计数
    max_sleep_time = MEMPOOL_MAX_POLL_INTERVAL  # 最大休眠时间（秒）
    min_sleep_time = MEMPOOL_MIN_POLL_INTERVAL   # 最小休眠时间（秒）
    current_sleep_time = min_sleep_time
    
    while not stop_signal.is_set():
        time.sleep(current_sleep_time)
        new_mempool_txids = get_mempool_transactions()
        new_txids = new_mempool_txids - mempool_txids - filter_txids    #新的查询减去上一轮的mempool_txids与过滤的txids
        
        if new_txids:
            # 发现新交易，重置轮询频率
            consecutive_empty_polls = 0
            current_sleep_time = min_sleep_time
            
            try:
                # Process transactions and get details
                tx_details, inscriptions_transactions, processed_txids = process_transactions(new_txids, processed_txids, True)
                
                if inscriptions_transactions:
                    log_info(f"新增 {len(inscriptions_transactions)} 笔铭文交易")
                    # 详细信息改为DEBUG级别
                    for idx, tx in enumerate(inscriptions_transactions, 1):
                        txid = tx['txid']
                        log_debug(f"铭文交易 #{idx}:")
                        try:
                            fee = inscriptions_cache[txid]['fee']
                        except KeyError:
                            fee = "未知"
                        log_debug(f"  交易ID: {txid}, fee: {fee}")
                        log_debug(f"  输出地址: {tx['output_address']}")
                        log_debug("  铭文内容:")
                        for inscription in tx['inscriptions']:
                            log_debug(f"    - 类型: {inscription['type']}")
                            if 'content' in inscription:
                                content_preview = inscription['content'][:150] + '...' if len(inscription['content']) > 150 else inscription['content']
                                log_debug(f"      内容预览: {content_preview}")
                        log_debug("  " + "-"*40)  # Separator line
                mempool_txids = new_mempool_txids  # Update mempool_txids to the latest set of mempool transactions
                cleanup_cache(mempool_txids)  # Clean up expired transaction data in the cache
            except Exception as e:
                log_error(f"Error processing transactions: {str(e)}")
        else:
            # 没有新交易，逐渐增加轮询间隔
            consecutive_empty_polls += 1
            if consecutive_empty_polls > 3:  # 连续3次空轮询后开始增加间隔
                current_sleep_time = min(current_sleep_time * 1.5, max_sleep_time)
                log_debug(f"调整轮询间隔为 {current_sleep_time:.1f} 秒 (连续空轮询 {consecutive_empty_polls} 次)")

#调试用,用于查询某笔交易的父交易手续费
def get_txid_ancestors_fee(txid):
    all_ancestors = []
    responses = rpc_call('getmempoolancestors',[txid, True])
    ancestors = responses
    if ancestors is not None:
        for ancestor_txid, ancestor_data in ancestors.items():
            fee_rate = calculate_fee_rate(ancestor_data)
            all_ancestors.append({
                'txid': ancestor_txid,
                'fee': fee_rate,
                'time': ancestor_data['time']
            })
    
    return all_ancestors

#调试用,用查询某笔交易的续费
def get_txid_fee(txid):
    data = {}
    tx_data = rpc_call('getmempoolentry',[txid])
    if tx_data is not None:
        fee_rate = calculate_fee_rate(tx_data)
        data = {
            'txid': txid,
            'fee': fee_rate,
            'time': tx_data['time']
        }
    return data

@app.get("/api/brc20/tick/{tick}", response_model=BRC20Response)
async def get_brc20_by_tick(
    tick: str = Path(..., description="BRC20 tick to search for")
):
    results = []
    
    # 将输入的tick转为小写用于比较
    tick_lower = tick.lower()
    
    # 遍历缓存中的所有交易，使用小写进行比较
    for txid, data in inscriptions_cache.items():
        for inscription in data['inscriptions']:
            if 'brc20' in inscription and inscription['brc20']['tick'].lower() == tick_lower:
                results.append(BRC20Result(
                    txid=txid,
                    tick=inscription['brc20']['tick'],  # 保持原始tick不变
                    operation=inscription['brc20']['operation'],
                    amount=inscription['brc20']['amount'],
                    fee=data['fee'],
                    output_address=data['output_address']
                ))
    
    if not results:
        raise HTTPException(status_code=404, detail=f"No BRC20 inscriptions found for tick {tick}")
        
    # 按照fee降序排序
    sorted_results = sorted(results, key=lambda x: x.fee, reverse=True)
    
    return BRC20Response(total=len(sorted_results), results=sorted_results)

# 添加新的缓存字典和缓存时间常量
TICK_CACHE_TIMEOUT = 30  # 缓存过期时间(秒)
tick_info_cache = {}  # {tick: {'data': {...}, 'timestamp': time.time(), 'valid': bool}}

async def get_tick_info(tick: str, force_update: bool = False) -> Optional[dict]:
    """获取tick信息,包含缓存处理"""
    current_time = time.time()
    
    # 记录原始输入
    #log_info(f"get_tick_info - 原始输入 tick: '{tick}'")
    
    # 检查缓存
    if not force_update and tick in tick_info_cache:
        cache_data = tick_info_cache[tick]
        if current_time - cache_data['timestamp'] < TICK_CACHE_TIMEOUT:
            if not cache_data.get('valid', True):
                log_debug(f"get_tick_info - 从缓存返回无效tick: '{tick}'")
                return None
            #log_info(f"get_tick_info - 从缓存返回数据: '{tick}'")
            return cache_data['data']
    
    # 调用外部API获取tick信息
    try:
        async with httpx.AsyncClient() as client:
            # 对tick进行URL编码，但保留已编码的字符
            from urllib.parse import quote, unquote
            
            # 记录编码前的tick
            #log_info(f"get_tick_info - 编码前的tick: '{tick}'")
            
            # 先解码以防重复编码
            decoded_tick = unquote(tick)
            #log_info(f"get_tick_info - 解码后的tick: '{decoded_tick}'")
            
            # 重新编码，保留特殊字符
            encoded_tick = quote(decoded_tick, safe='%')
            #log_info(f"get_tick_info - 编码后的tick: '{encoded_tick}'")
            
            full_url = f"{brc20_api_url}{brc20_api_path}/{encoded_tick}/info"
            #log_info(f"get_tick_info - 请求URL: '{full_url}'")
            
            # 添加授权头
            headers = {
                "Authorization": brc20_api_auth
            }
            
            response = await client.get(full_url, headers=headers)
            #log_info(f"get_tick_info - 响应状态码: {response.status_code}")
            
            if response.status_code == 404:
                # 缓存无效的tick
                tick_info_cache[tick] = {
                    'data': None,
                    'timestamp': current_time,
                    'valid': False
                }
                log_warning(f"get_tick_info - tick不存在: '{tick}'")
                return None
            if response.status_code == 400:
                # 缓存无效的tick
                tick_info_cache[tick] = {
                    'data': None,
                    'timestamp': current_time,
                    'valid': False
                }
                log_warning(f"get_tick_info - tick无效: '{tick}'")
                return None    
            data = response.json()
            #log_info(f"get_tick_info - 响应数据: {data}")
            
            if data.get("code") == 0 and "data" in data:
                original_data = data["data"]
                
                # 将新API格式转换为旧API格式，保持兼容性
                # 安全地处理可能是浮点数的字符串
                def safe_convert_to_int_with_decimal(value, decimal):
                    try:
                        # 如果是浮点数字符串，先转换为浮点数再转换为整数
                        if '.' in str(value):
                            return str(int(float(value) * (10 ** decimal)))
                        else:
                            return str(int(value) * (10 ** decimal))
                    except (ValueError, TypeError):
                        return "0"
                
                tick_data = {
                    'tick': original_data.get('ticker', ''),
                    'inscriptionId': original_data.get('inscriptionId', ''),
                    'inscriptionNumber': original_data.get('inscriptionNumber', 0),
                    'supply': safe_convert_to_int_with_decimal(original_data.get('max', '0'), original_data.get('decimal', 18)),
                    'minted': safe_convert_to_int_with_decimal(original_data.get('confirmedMinted', '0'), original_data.get('decimal', 18)),
                    'decimal': original_data.get('decimal', 18),
                    'limitPerMint': safe_convert_to_int_with_decimal(original_data.get('limit', '0'), original_data.get('decimal', 18)),
                    'selfMint': original_data.get('selfMint', False),
                    'deployBy': {'address': original_data.get('creator', '')},
                    'deployHeight': original_data.get('deployHeight', 0),
                    'deployBlocktime': original_data.get('deployBlocktime', 0)
                }
                
                # 更新缓存，使用原始tick作为键
                tick_info_cache[tick] = {
                    'data': tick_data,
                    'timestamp': current_time,
                    'valid': True
                }
                #log_info(f"get_tick_info - 成功获取tick数据: '{tick}'")
                return tick_data
            log_warning(f"get_tick_info - 无效的响应数据格式: {data}")
            return None
    except Exception as e:
        log_error(f"get_tick_info - 获取tick信息失败: {str(e)}, tick: '{tick}'")
        return None

def calculate_remaining_mint(supply: str, minted: str, decimal: int) -> str:
    """计算剩余可铸造量"""
    try:
        supply_num = int(supply)
        minted_num = int(minted)
        remaining = supply_num - minted_num
        if remaining <= 0:
            return "0"
        return str(remaining // (10 ** decimal))
    except Exception as e:
        log_error(f"计算剩余铸造量出错: {str(e)}")
        return "0"

@app.get("/api/brc20/trending", response_model=BRC20TrendingResponse)
async def get_brc20_trending(
    limit: int = Query(default=20, description="Number of results to return", ge=1, le=100)
):
    tick_stats = {}
    valid_results = []
    
    # 创建inscriptions_cache的快照
    with inscriptions_cache_lock:
        cache_snapshot = dict(inscriptions_cache)
    
    # 使用快照而不是直接使用inscriptions_cache
    for txid, data in cache_snapshot.items():
        for inscription in data['inscriptions']:
            if 'brc20' in inscription:
                brc20_data = inscription['brc20']
                
                if brc20_data['operation'] != 'mint':
                    continue
                    
                tick = brc20_data['tick'].lower()
                
                if tick not in tick_stats:
                    tick_stats[tick] = {
                        'txids': set(),
                        'amounts': [],
                        'fees': [],
                        'addresses': set(),
                        'all_fees': []	# 添加一个列表存储所有fee
                    }
                
                stats = tick_stats[tick]
                stats['txids'].add(txid)
                
                try:
                    amount = float(brc20_data['amount'])
                    stats['amounts'].append(amount)
                except (ValueError, TypeError):
                    continue
                
                stats['fees'].append(data['fee'])
                stats['addresses'].add(data['output_address'])
                stats['all_fees'].append(data['fee'])	 # 保存所有fee用于计算中位数
    
    # 处理统计数据并获取tick信息
    for tick, stats in tick_stats.items():
        if not stats['txids']:
            continue
            
        # 从缓存获取tick信息
        tick_info = None
        if tick in tick_info_cache:
            # 检查缓存是否标记为无效
            if not tick_info_cache[tick].get('valid', True):
                continue
            tick_info = tick_info_cache[tick]['data']
        else:
            tick_info = await get_tick_info(tick)
            
        if tick_info is None:
            continue
            
        # 获取原始数据
        supply_raw = tick_info.get('supply', '0')
        minted_raw = tick_info.get('minted', '0')
        decimal = tick_info.get('decimal', 18)
        limit_per_mint_raw = tick_info.get('limitPerMint', '0')
        
        # 检查是否已经全部mint完成
        if supply_raw == minted_raw:
            continue
            
        # 处理数据为可读格式
        supply = format_decimal_number(supply_raw, decimal)
        minted = format_decimal_number(minted_raw, decimal)
        remaining_mint = format_decimal_number(str(int(supply_raw) - int(minted_raw)), decimal)
        limit_per_mint = format_decimal_number(limit_per_mint_raw, decimal)
        remaining_mint_count = calculate_remaining_mint_count(remaining_mint, limit_per_mint)
        
        # 计算fee的中位数
        median_fee = float(np.median(stats['all_fees'])) if stats['all_fees'] else 0.0
        total_amount = sum(stats['amounts'])
        
        valid_results.append(BRC20TrendingItem(
            tick=tick,
            total_txs=len(stats['txids']),
            total_amount=str(total_amount),
            unique_addresses=len(stats['addresses']),
            median_fee=round(median_fee, 2),
            inscriptionId=tick_info.get('inscriptionId'),
            inscriptionNumber=tick_info.get('inscriptionNumber'),
            supply=supply,
            supply_raw=supply_raw,
            selfMint=tick_info.get('selfMint'),
            limitPerMint=limit_per_mint,
            minted=minted,
            decimal=decimal,
            remaining_mint=remaining_mint,
            remaining_mint_count=remaining_mint_count
        ))
    
    # 按照交易数量降序排序
    sorted_results = sorted(valid_results, key=lambda x: x.total_txs, reverse=True)
    limited_results = sorted_results[:limit]
    
    return BRC20TrendingResponse(
        total=len(sorted_results),
        results=limited_results
    )

@app.get("/api/brc20/rank/{tick}/{txid}", response_model=TxidRankResponse)
async def get_brc20_txid_rank(
    tick: str = Path(..., description="BRC20 tick to search for"),
    txid: str = Path(..., description="Transaction ID to get rank for")
):
    tick_lower = tick.lower()
    results = []
    
    # 遍历缓存获取所有相关的mint交易
    for tx_id, data in inscriptions_cache.items():
        for inscription in data['inscriptions']:
            if 'brc20' in inscription and inscription['brc20']['tick'].lower()  == tick_lower:
                if inscription['brc20']['operation'] == 'mint':
                    results.append({
                        'txid': tx_id,
                        'fee': data['fee']
                    })
    
    if not results:
        raise HTTPException(status_code=404, detail=f"No mint transactions found for tick {tick}")
    
    # 按fee降序排序
    sorted_results = sorted(results, key=lambda x: x['fee'], reverse=True)
    
    # 查找目标txid的排名
    rank = None
    fee = None
    for idx, item in enumerate(sorted_results, 1):
        if item['txid'] == txid:
            rank = idx
            fee = item['fee']
            break
    
    if rank is None:
        raise HTTPException(status_code=404, detail=f"Transaction {txid} not found in mint list for tick {tick}")
    
    return TxidRankResponse(
        txid=txid,
        tick=tick,
        rank=rank,
        total=len(sorted_results),
        fee=fee
    )

@app.get("/api/brc20/tick/{tick}/distribution")
async def get_brc20_fee_distribution(
    tick: str = Path(..., description="BRC20 tick to search for")
):
    tick_lower = tick.lower()
    fees = []
    
    # 遍历缓存获取所有相关的mint交易的fee
    for tx_id, data in inscriptions_cache.items():
        for inscription in data['inscriptions']:
            if 'brc20' in inscription and inscription['brc20']['tick'].lower() == tick_lower:
                if inscription['brc20']['operation'] == 'mint':
                    fees.append(data['fee'])
    
    if not fees:
        raise HTTPException(status_code=404, detail=f"No mint transactions found for tick {tick}")
    
    # 生成fee区间
    intervals, decimal_places = generate_intervals(fees)
    
    # 统计每个区间的交易数量
    distribution = {}
    for start, end in intervals:
        count = sum(1 for fee in fees if start <= fee < end)
        if count > 0:  # 只返回有交易的区间
            distribution[f"{start:.{decimal_places}f}-{end:.{decimal_places}f}"] = count
    
    return distribution

# 添加缓存文件保存和加载函数
def save_tick_cache():
    try:
        cache_path = os.path.join(os.path.dirname(__file__), 'tick_cache.json')
        cache_data = {
            tick: {
                'data': data['data'],
                'timestamp': data['timestamp'],
                'valid': data.get('valid', True)  # 保存valid字段
            }
            for tick, data in tick_info_cache.items()
        }
        with open(cache_path, 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, indent=4)
        log_debug(f"保存了 {len(cache_data)} 个tick缓存信息")
    except Exception as e:
        log_error(f"保存tick缓存失败: {str(e)}")

def load_tick_cache():
    try:
        cache_path = os.path.join(os.path.dirname(__file__), 'tick_cache.json')
        if os.path.exists(cache_path):
            with open(cache_path, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
                tick_info_cache.update(cache_data)
            log_debug(f"加载了 {len(cache_data)} 个tick缓存信息")
    except Exception as e:
        log_error(f"加载tick缓存失败: {str(e)}")

# 修改数值处理函数
def format_decimal_number(number: str, decimal: int) -> str:
    """将带有decimal位数的数字转换为可读格式"""
    try:
        num = int(number)
        return str(num // (10 ** decimal))
    except Exception as e:
        log_error(f"格式化数据出错: {str(e)}")
        return "0"

def calculate_remaining_mint_count(remaining: str, limit_per_mint: str) -> int:
    """计算还可以铸造多少次"""
    try:
        remaining_num = int(remaining)
        limit_num = int(limit_per_mint)
        if limit_num == 0:
            return 0
        return math.ceil(remaining_num / limit_num)
    except Exception as e:
        log_error(f"计算剩余铸造次数出错: {str(e)}")
        return 0

class BRC20TickInfo(BaseModel):
    """BRC20 tick信息的返回模型"""
    inscriptionId: Optional[str]
    inscriptionNumber: Optional[int]
    supply: str
    supply_raw: str
    minted: str
    minted_raw: str
    decimal: int
    limitPerMint: str
    limitPerMint_raw: str
    remaining_mint: str
    remaining_mint_count: int
    selfMint: bool
    deployBy: Optional[dict]
    deployHeight: Optional[int]
    deployBlocktime: Optional[int]
    fee_distribution: dict
    unique_addresses: int
    median_fee: float
    total_txs: int
    total_amount: str
    pperchain: Optional[int] = None  # 添加新字段，默认为None
    pperchain_fee: Optional[float] = None  # 添加费率下限字段

@app.get("/api/brc20/tick/{tick}/info", response_model=BRC20TickInfo)
async def get_brc20_tick_info(
    tick: str = Path(..., description="BRC20 tick to search for"),
    force_update: bool = Query(False, description="Force update from external API"),
    fee: Optional[str] = Query(None, description="费率范围，格式为min,max，例如1.01,5.21"),
    nTx: Optional[int] = Query(None, description="最大返回的交易数量")
):
    """
    获取或更新tick信息
    
    返回指定BRC20代币的详细信息和统计数据
    
    - **fee**: 可选，费率范围，格式为min,max，例如1.01,5.21
    - **nTx**: 可选，最大返回的交易数量
    """
    
    # 强制更新或缓存过期时从外部API获取
    tick_info = await get_tick_info(tick, force_update=force_update)
    if tick_info is None:
        raise HTTPException(status_code=404, detail=f"Failed to get info for tick {tick}")
    
    # 获取原始数据
    supply_raw = tick_info.get('supply', '0')
    minted_raw = tick_info.get('minted', '0')
    decimal = tick_info.get('decimal', 18)
    limit_per_mint_raw = tick_info.get('limitPerMint', '0')
    
    # 处理数据为可读格式，增加错误处理
    try:
        supply = format_decimal_number(supply_raw, decimal)
        minted = format_decimal_number(minted_raw, decimal)
        supply_int = int(float(supply_raw))
        minted_int = int(float(minted_raw))
        remaining_mint = format_decimal_number(str(supply_int - minted_int), decimal)
        limit_per_mint = format_decimal_number(limit_per_mint_raw, decimal)
        remaining_mint_count = calculate_remaining_mint_count(remaining_mint, limit_per_mint)
    except (ValueError, OverflowError) as e:
        log_error(f"处理代币数据时出错，tick: {tick}, error: {str(e)}")
        # 使用原始字符串作为备用方案
        supply = supply_raw
        minted = minted_raw
        remaining_mint = "0"
        limit_per_mint = limit_per_mint_raw
        remaining_mint_count = 0
    
    # 获取fee分布数据、唯一地址数和交易统计
    fees = []
    addresses = set()
    amounts = []  # 用于统计总铸造量
    txs = set()  # 用于统计总交易数
    tick_lower = tick.lower()
    all_tx_data = []  # 存储所有交易数据用于fee和nTx处理
    
    for tx_id, data in inscriptions_cache.items():
        for inscription in data['inscriptions']:
            if 'brc20' in inscription and inscription['brc20']['tick'].lower() == tick_lower:
                if inscription['brc20']['operation'] == 'mint':
                    fees.append(data['fee'])
                    addresses.add(data['output_address'])
                    txs.add(tx_id)
                    all_tx_data.append({'txid': tx_id, 'fee': data['fee']})
                    try:
                        amount = float(inscription['brc20']['amount'])
                        amounts.append(amount)
                    except (ValueError, TypeError):
                        continue
    
    # 计算fee中位数
    median_fee = float(np.median(fees)) if fees else 0.0
    
    # 生成fee区间分布
    distribution = {}
    if fees:
        intervals, decimal_places = generate_intervals(fees)
        for start, end in intervals:
            count = sum(1 for fee in fees if start <= fee < end)
            if count > 0:
                distribution[f"{start:.{decimal_places}f}-{end:.{decimal_places}f}"] = count
    
    # 处理新增的fee和nTx参数
    pperchain_value = None
    fee_min = None
    fee_max = None
    if fee is not None and nTx is not None:
        try:
            # 解析fee参数
            fee_min, fee_max = map(float, fee.split(','))
            
            # 从缓存中统计符合条件的交易数量
            tx_count = sum(1 for tx_data in all_tx_data if fee_min <= tx_data['fee'] <= fee_max)
            
            # 如果数量超过nTx，则限制为nTx
            pperchain_value = min(tx_count, nTx)
            
            log_debug(f"BRC20代币 {tick} 费率区间 {fee_min}-{fee_max} 的交易数量: {tx_count}，限制为: {pperchain_value}")
        except Exception as e:
            log_error(f"处理fee参数出错: {str(e)}")
            # 出错时不设置pperchain值，继续执行
    
    return BRC20TickInfo(
        inscriptionId=tick_info.get('inscriptionId'),
        inscriptionNumber=tick_info.get('inscriptionNumber'),
        supply=supply,
        supply_raw=supply_raw,
        minted=minted,
        minted_raw=minted_raw,
        decimal=decimal,
        limitPerMint=limit_per_mint,
        limitPerMint_raw=limit_per_mint_raw,
        remaining_mint=remaining_mint,
        remaining_mint_count=remaining_mint_count,
        selfMint=tick_info.get('selfMint', False),
        deployBy=tick_info.get('deployBy'),
        deployHeight=tick_info.get('deployHeight'),
        deployBlocktime=tick_info.get('deployBlocktime'),
        fee_distribution=distribution,
        unique_addresses=len(addresses),
        median_fee=round(median_fee, 2),
        total_txs=len(txs),  # 添加总交易数
        total_amount=str(sum(amounts)),  # 添加总铸造量
        pperchain=pperchain_value,
        pperchain_fee=fee_min
    )

# 在更新inscriptions_cache的地方也需要使用锁
def update_inscriptions_cache(new_data):
    with inscriptions_cache_lock:
        inscriptions_cache.update(new_data)

def remove_from_cache(txid):
    with inscriptions_cache_lock:
        if txid in inscriptions_cache:
            del inscriptions_cache[txid]

# 添加新的数据模型用于区块铭文统计
class BRC20BlockStat(BaseModel):
    """单个区块中的BRC20统计"""
    tick: str
    tick_name: Optional[str] = None
    tx_count: int
    address_count: int
    total_amount: str
    median_fee: float

class BRC20BlockInfo(BaseModel):
    """区块BRC20信息"""
    block_height: int
    brc20_count: int  # 该区块中的BRC20代币种类数
    brc20_stats: List[BRC20BlockStat]

class BRC20RecentBlocksResponse(BaseModel):
    """最近区块的BRC20信息响应"""
    recent_blocks: List[BRC20BlockInfo]

def scan_brc20_block(block_height: int) -> BRC20BlockInfo:
    """
    扫描指定区块，提取BRC20铭文交易，并将结果缓存
    
    返回: BRC20BlockInfo 对象
    """
    global block_cache, scanned_blocks
    
    # 如果已经扫描过并且在缓存中，直接返回缓存结果
    if block_height in block_cache:
        log_debug(f"从缓存返回区块 {block_height} 的 BRC20 数据")
        return block_cache[block_height]
    
    try:
        log_debug(f"开始扫描区块 {block_height} 的 BRC20 交易")
        
        # 获取区块数据
        blockhash = getblockhash(block_height)
        if not blockhash:
            log_error(f"无法获取区块 {block_height} 的哈希")
            empty_result = BRC20BlockInfo(block_height=block_height, brc20_count=0, brc20_stats=[])
            block_cache[block_height] = empty_result
            return empty_result
            
        block_data = getblock(blockhash)
        if not block_data:
            log_error(f"无法获取区块 {block_height} 的数据")
            empty_result = BRC20BlockInfo(block_height=block_height, brc20_count=0, brc20_stats=[])
            block_cache[block_height] = empty_result
            return empty_result
        
        # 统计每个代币的数据
        tick_stats = {}  # {tick: {'fees': [], 'addresses': set(), 'amounts': []}}
        txs = block_data.get("tx", [])
        
        # 遍历所有交易，找出包含BRC20铭文的交易并直接统计
        for tx in txs:
            txid = tx["txid"]
            
            # 解析交易中的铭文
            for vin in tx['vin']:
                txinwitness = vin.get('txinwitness', [])
                for witness in txinwitness:
                    try:
                        inscriptions = analyze_inscriptions(witness, txid)
                        if inscriptions:
                            # 获取输出地址
                            output_address = None
                            for vout in tx['vout']:
                                if 'scriptPubKey' in vout and 'address' in vout['scriptPubKey']:
                                    output_address = vout['scriptPubKey']['address']
                                    break
                            
                            # 计算费率
                            try:
                                fee_satoshi = tx.get("fee", 0) * 100000000  # BTC转换为satoshi
                                vsize = tx.get("vsize", tx.get("size", 1))  # 优先使用vsize，回退到size
                                fee_rate = fee_satoshi / vsize if vsize > 0 else 0
                                fee = round(fee_rate, 2)
                            except (TypeError, ValueError) as e:
                                log_debug(f"计算交易 {txid} 费率时出错: {str(e)}")
                                fee = 0
                            
                            # 处理每个铭文
                            for inscription in inscriptions:
                                if inscription['type'] in ['text/plain;charset=utf-8', 'text/plain']:
                                    brc20_data = parse_brc20_inscription(inscription['content'])
                                    if brc20_data and brc20_data['operation'] == 'mint':
                                        tick = brc20_data['tick'].lower()
                                        
                                        if tick not in tick_stats:
                                            tick_stats[tick] = {
                                                'fees': [],
                                                'addresses': set(),
                                                'amounts': [],
                                                'tx_count': 0
                                            }
                                        
                                        tick_stats[tick]['fees'].append(fee)
                                        if output_address:
                                            tick_stats[tick]['addresses'].add(output_address)
                                        
                                        try:
                                            amount = float(brc20_data['amount'])
                                            tick_stats[tick]['amounts'].append(amount)
                                        except (ValueError, TypeError):
                                            pass
                                        
                                        tick_stats[tick]['tx_count'] += 1
                    except Exception as e:
                        log_debug(f"解析区块 {block_height} 中交易 {txid} 的铭文时出错: {str(e)}")
                        continue
        
        # 生成统计结果
        brc20_stats = []
        for tick, stats in tick_stats.items():
            if not stats['fees']:
                continue
                
            # 计算中位数费率
            median_fee = float(np.median(stats['fees'])) if stats['fees'] else 0.0
            total_amount = sum(stats['amounts'])
            
            # 获取代币名称（如果有缓存）
            tick_name = None
            if tick in tick_info_cache:
                cache_data = tick_info_cache[tick]
                if cache_data.get('valid', True) and cache_data.get('data'):
                    tick_name = cache_data['data'].get('tick', tick)
            
            brc20_stats.append(BRC20BlockStat(
                tick=tick,
                tick_name=tick_name,
                tx_count=stats['tx_count'],
                address_count=len(stats['addresses']),
                total_amount=str(total_amount),
                median_fee=round(median_fee, 2)
            ))
        
        # 按交易数量降序排序
        brc20_stats.sort(key=lambda x: x.tx_count, reverse=True)
        
        # 创建结果对象
        result = BRC20BlockInfo(
            block_height=block_height,
            brc20_count=len(brc20_stats),
            brc20_stats=brc20_stats
        )
        
        # 将结果缓存
        block_cache[block_height] = result
        scanned_blocks.add(block_height)
        
        # 清理旧缓存
        clean_old_block_cache_files()
        
        # 保存更新后的缓存到文件
        save_block_cache()
        
        log_debug(f"区块 {block_height} 扫描完成，找到 {len(brc20_stats)} 个 BRC20 代币")
        return result
        
    except Exception as e:
        log_error(f"扫描区块 {block_height} 时出错: {str(e)}")
        empty_result = BRC20BlockInfo(block_height=block_height, brc20_count=0, brc20_stats=[])
        block_cache[block_height] = empty_result  # 缓存空结果以避免重复扫描
        return empty_result



def initialize_recent_blocks():
    """异步初始化最近区块的缓存"""
    def init_blocks():
        try:
            current_height = getblockcount()
            if current_height == 0:
                log_error("无法获取当前区块高度进行初始化")
                return
            
            # 扫描最近的区块，数量可配置
            start_height = max(1, current_height - INITIAL_BLOCK_SCAN_COUNT + 1)
            
            # 检查哪些区块需要扫描（避免重复扫描已缓存的区块）
            heights_to_scan = []
            cached_count = 0
            for height in range(start_height, current_height + 1):
                if height in block_cache:
                    cached_count += 1
                    log_debug(f"区块 {height} 已缓存，跳过扫描")
                else:
                    heights_to_scan.append(height)
            
            if heights_to_scan:
                log_info(f"后台初始化区块缓存，已缓存 {cached_count} 个区块，需要扫描区块: {heights_to_scan}")
                for height in heights_to_scan:
                    scan_brc20_block(height)
            else:
                log_info(f"所有区块已缓存，无需重复扫描")
            
            log_info(f"区块缓存初始化完成，共缓存 {len(block_cache)} 个区块")
            
        except Exception as e:
            log_error(f"初始化区块缓存时出错: {str(e)}")
    
    # 在后台线程中执行初始化，不阻塞启动
    init_thread = threading.Thread(target=init_blocks, daemon=True)
    init_thread.start()
    log_info("区块缓存初始化已在后台启动")

def scan_recent_brc20_blocks():
    """
    BRC20 区块扫描监听线程
    自动检测新区块并扫描其中的 BRC20 铭文交易
    """
    global block_cache, scanned_blocks
    
    try:
        # 延迟启动，等待应用完全启动
        time.sleep(5)
        
        # 获取当前区块高度作为起始点
        current_height = getblockcount()
        if current_height == 0:
            log_error("初始化区块高度获取失败，稍后重试")
            time.sleep(10)
            return
        
        log_info(f"BRC20 区块监听线程启动，当前区块高度: {current_height}")
        
        # 持续监听新区块
        while not stop_signal.is_set():
            try:
                # 获取当前最新区块高度
                new_height = getblockcount()
                
                # 检查是否有新区块产生
                if new_height > current_height and new_height > 0:
                    log_info(f"发现新区块: {current_height + 1} 到 {new_height}")
                    
                    # 只扫描新增的区块
                    for height in range(current_height + 1, new_height + 1):
                        if height not in scanned_blocks:  # 避免重复扫描
                            log_info(f"自动扫描新区块 {height} 的 BRC20 交易")
                            block_brc20_data = scan_brc20_block(height)
                            
                            # 记录扫描状态
                            scanned_blocks.add(height)
                            
                            # 如果发现BRC20交易，记录日志
                            if block_brc20_data and block_brc20_data.brc20_count > 0:
                                total_tx_count = sum(stat.tx_count for stat in block_brc20_data.brc20_stats)
                                log_info(f"区块 {height} 发现 {block_brc20_data.brc20_count} 种 BRC20 代币，共 {total_tx_count} 笔交易")
                            else:
                                log_debug(f"区块 {height} 未发现 BRC20 交易")
                    
                    # 更新当前高度
                    current_height = new_height
                    
            except Exception as e:
                log_error(f"BRC20 区块扫描监听发生错误: {str(e)}")
                time.sleep(15)  # 出错后等待较短时间再重试
                continue
            
            # 自适应区块检查间隔 - 比特币区块平均10分钟出一个
            time.sleep(BLOCK_CHECK_INTERVAL)
            
    except Exception as e:
        log_error(f"区块监听线程启动失败: {str(e)}")
        return

@app.get("/api/brc20/recent", response_model=BRC20RecentBlocksResponse)
async def get_recent_brc20_data(limit: int = Query(6, description="返回最近的区块数量", ge=1, le=20)):
    """
    获取最近几个区块的 BRC20 铭文铸造记录
    
    返回最近指定数量区块中的 BRC20 代币铸造统计信息
    """
    try:
        # 优先从缓存中获取数据
        if block_cache:
            # 从缓存中获取最近的区块
            recent_block_heights = sorted(block_cache.keys(), reverse=True)[:limit]
            log_debug(f"从缓存获取最近 {len(recent_block_heights)} 个区块的数据")
        else:
            # 如果缓存为空，扫描最近的区块
            current_height = getblockcount()
            if current_height == 0:
                raise HTTPException(status_code=500, detail="无法获取当前区块高度")
            
            start_height = max(1, current_height - limit + 1)
            recent_block_heights = list(range(start_height, current_height + 1))
            log_info(f"缓存为空，扫描区块 {start_height} 到 {current_height}")
            
            # 扫描这些区块到缓存中
            for height in recent_block_heights:
                scan_brc20_block(height)
            
            recent_block_heights = sorted(recent_block_heights, reverse=True)
        
        result = []
        
        # 从缓存中构建结果
        for block_height in recent_block_heights:
            block_info = block_cache.get(block_height)
            
            if block_info:
                # 直接使用缓存的统计结果
                result.append(block_info)
            else:
                # 缓存中没有数据，扫描该区块
                log_debug(f"区块 {block_height} 不在缓存中，进行扫描")
                scanned_block_info = scan_brc20_block(block_height)
                result.append(scanned_block_info)
        
        log_info(f"成功获取 {len(result)} 个区块的 BRC20 数据")
        return BRC20RecentBlocksResponse(recent_blocks=result)
        
    except Exception as e:
        log_error(f"查询最近的区块 BRC20 数据出错: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

@app.get("/api/brc20/block/{block_height}")
async def get_brc20_block_data(block_height: int = Path(..., description="区块高度")):
    """
    获取指定区块的 BRC20 铭文铸造记录
    
    返回指定区块中的 BRC20 代币铸造统计信息
    """
    try:
        log_info(f"查询区块 {block_height} 的 BRC20 数据")
        
        # 从缓存或扫描获取指定区块数据
        block_info = scan_brc20_block(block_height)
        
        # 转换为字典格式返回
        return {
            "block_height": block_info.block_height,
            "brc20_count": block_info.brc20_count,
            "brc20_stats": [
                {
                    "tick": stat.tick,
                    "tick_name": stat.tick_name,
                    "tx_count": stat.tx_count,
                    "address_count": stat.address_count,
                    "total_amount": stat.total_amount,
                    "median_fee": stat.median_fee
                }
                for stat in block_info.brc20_stats
            ]
        }
        
    except Exception as e:
        log_error(f"查询区块 {block_height} 的 BRC20 数据出错: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

# 添加区块缓存文件相关函数
def save_block_cache():
    """保存区块缓存到文件"""
    try:
        cache_path = os.path.join(os.path.dirname(__file__), 'block_cache.json')
        # 转换为可序列化的格式
        serializable_cache = {}
        for height, block_info in block_cache.items():
            serializable_cache[str(height)] = {
                "block_height": block_info.block_height,
                "brc20_count": block_info.brc20_count,
                "brc20_stats": [
                    {
                        "tick": stat.tick,
                        "tick_name": stat.tick_name,
                        "tx_count": stat.tx_count,
                        "address_count": stat.address_count,
                        "total_amount": stat.total_amount,
                        "median_fee": stat.median_fee
                    }
                    for stat in block_info.brc20_stats
                ]
            }
        
        cache_data = {
            'block_cache': serializable_cache,
            'scanned_blocks': list(scanned_blocks),
            'last_update': time.time()
        }
        with open(cache_path, 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, indent=2)
        log_debug(f"保存了 {len(block_cache)} 个区块统计缓存")
    except Exception as e:
        log_error(f"保存区块缓存失败: {str(e)}")

def load_block_cache():
    """从文件加载区块缓存"""
    global block_cache, scanned_blocks
    try:
        cache_path = os.path.join(os.path.dirname(__file__), 'block_cache.json')
        if os.path.exists(cache_path):
            with open(cache_path, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            # 加载区块缓存
            loaded_cache = cache_data.get('block_cache', {})
            for height_str, data in loaded_cache.items():
                height = int(height_str)
                # 直接加载统计结果
                brc20_stats = []
                for stat_data in data.get('brc20_stats', []):
                    brc20_stats.append(BRC20BlockStat(
                        tick=stat_data['tick'],
                        tick_name=stat_data.get('tick_name'),
                        tx_count=stat_data['tx_count'],
                        address_count=stat_data['address_count'],
                        total_amount=stat_data['total_amount'],
                        median_fee=stat_data['median_fee']
                    ))
                
                block_cache[height] = BRC20BlockInfo(
                    block_height=data['block_height'],
                    brc20_count=data['brc20_count'],
                    brc20_stats=brc20_stats
                )
            
            scanned_blocks.update(set(cache_data.get('scanned_blocks', [])))
            
            log_info(f"加载了 {len(block_cache)} 个区块统计缓存")
            return True
    except Exception as e:
        log_error(f"加载区块缓存失败: {str(e)}")
    return False

def clean_old_block_cache_files():
    """清理超过限制的旧区块缓存，只保留最近的20个区块"""
    global block_cache, scanned_blocks
    
    if len(block_cache) > MAX_RECENT_BLOCKS:
        # 获取所有区块高度并排序
        all_heights = sorted(block_cache.keys())
        # 计算需要删除的区块数量
        to_remove = len(all_heights) - MAX_RECENT_BLOCKS
        
        # 删除最旧的区块
        for height in all_heights[:to_remove]:
            if height in block_cache:
                del block_cache[height]
            if height in scanned_blocks:
                scanned_blocks.discard(height)
        
        log_debug(f"清理了 {to_remove} 个旧区块缓存，保留最近 {MAX_RECENT_BLOCKS} 个区块")
        
        # 保存清理后的缓存
        save_block_cache()

if __name__ == "__main__":
    # 启动 FastAPI 应用
    server_host = config.get('server', {}).get('host', '0.0.0.0')
    server_port = config.get('server', {}).get('port', 5001)
    
    log_info(f"启动API服务器 - 地址: {server_host}:{server_port}")
    
    server_config = Config(app=app, host=server_host, port=server_port)
    server = Server(config=server_config)
    server.run()