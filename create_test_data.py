# -*- coding: utf-8 -*-
"""
创建测试数据，包含重复的铸造地址
"""
import openpyxl
from openpyxl.styles import Font, Alignment

def create_test_excel():
    """创建包含重复铸造地址的测试Excel文件"""
    
    # 测试数据 - 包含重复的铸造地址
    test_data = [
        # 编号, 铸造地址, 收货地址, 交易哈希, 区块编号
        [1, "bc1peve9pqv3k9hwfc57pu8mnjtdlyldp8jha57dgvqc3kexvrk7r43qlyqgsz", "bc1pxwu6adzrdp8y7g5a2ywsv9jwewdjnhpck367tcxrtjcmyf4a78hs5hfjzx", "86349755fd5d89bc83a969449e56233543a74dfc93287aed3ff29f3d80d7a2d7", 908119],
        [2, "bc1p299cpnnkhy9alh44cca2dekjmfur9tngzwsqtvx709mfj0rdl5asxrcvg0", "bc1psxu6wennk35jfk26wwclc49fzyutyqcmpyc4dvxj7scmt7gendnsptza6x", "903a84c11c909712ca2df887c2acbb63800f520fac4875b09e0fcbbb58306eed", 908123],
        # 重复的铸造地址1
        [3, "bc1peve9pqv3k9hwfc57pu8mnjtdlyldp8jha57dgvqc3kexvrk7r43qlyqgsz", "bc1paaabbbcccdddeeefffggghhhiiijjjkkklllmmmnnnooopppqqqrrrssstt", "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef", 908125],
        [4, "bc1ptest1234567890abcdefghijklmnopqrstuvwxyz1234567890abcdefgh", "bc1preceive1234567890abcdefghijklmnopqrstuvwxyz1234567890abcd", "abcdef1234567890abcdef1234567890abcdef1234567890abcdef1234567890", 908130],
        # 重复的铸造地址2
        [5, "bc1p299cpnnkhy9alh44cca2dekjmfur9tngzwsqtvx709mfj0rdl5asxrcvg0", "bc1pxxxyyyzzzaaabbbcccdddeeefffggghhhiiijjjkkklllmmmnnnoooppp", "fedcba0987654321fedcba0987654321fedcba0987654321fedcba0987654321", 908135],
        # 重复的铸造地址1 (第三次)
        [6, "bc1peve9pqv3k9hwfc57pu8mnjtdlyldp8jha57dgvqc3kexvrk7r43qlyqgsz", "bc1pthirdtime1234567890abcdefghijklmnopqrstuvwxyz1234567890", "333333333333333333333333333333333333333333333333333333333333333", 908140],
        [7, "bc1punique1234567890abcdefghijklmnopqrstuvwxyz1234567890abcdef", "bc1puniquerecv1234567890abcdefghijklmnopqrstuvwxyz1234567890", "777777777777777777777777777777777777777777777777777777777777777", 908145],
    ]
    
    # 创建工作簿
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "交易记录"
    
    # 设置表头
    headers = ["编号", "铸造地址", "收货地址", "交易哈希", "区块编号"]
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True)
        cell.alignment = Alignment(horizontal='center')
    
    # 写入测试数据
    for row_idx, data_row in enumerate(test_data, 2):
        for col_idx, value in enumerate(data_row, 1):
            ws.cell(row=row_idx, column=col_idx, value=value)
    
    # 调整列宽
    column_widths = [8, 45, 45, 70, 12]
    for col, width in enumerate(column_widths, 1):
        ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width
    
    # 保存文件
    filename = "test_transactions_with_duplicates.xlsx"
    wb.save(filename)
    
    print(f"测试文件已创建: {filename}")
    print(f"包含 {len(test_data)} 条记录")
    print("重复情况:")
    print("  - bc1peve9pqv3k9hwfc57pu8mnjtdlyldp8jha57dgvqc3kexvrk7r43qlyqgsz (3次重复)")
    print("  - bc1p299cpnnkhy9alh44cca2dekjmfur9tngzwsqtvx709mfj0rdl5asxrcvg0 (2次重复)")
    
    return filename

if __name__ == "__main__":
    create_test_excel()
