module.exports = {
  apps: [
    {
      name: 'brc20_api',
      script: '/home/<USER>/venv/bin/python',
      args: 'brc20.py',
      cwd: '/home/<USER>',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '8G',
      env: {
        NODE_ENV: 'production',
        PYTHONPATH: '/home/<USER>',
        PYTHONUNBUFFERED: '1'
      },
      error_file: '/home/<USER>/logs/err.log',
      out_file: '/home/<USER>/logs/out.log',
      log_file: '/home/<USER>/logs/combined.log',
      time: true,
      log_date_format: 'YYYY-MM-DD HH:mm:ss',
      merge_logs: true,
      kill_timeout: 30000,
      restart_delay: 3000
    }
  ]
}; 