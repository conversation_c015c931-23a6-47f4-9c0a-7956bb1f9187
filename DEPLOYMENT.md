# BRC20 API 部署指南

## 📦 文件说明

- `ecosystem.config.js` - PM2 配置文件
- `pm2_manager.sh` - PM2 管理脚本
- `brc20_api.service` - systemd 服务配置（备选）
- `brc20.py` - 主程序

## 🚀 PM2 部署（推荐）

### 1. 安装 PM2
```bash
# 如果没有安装 PM2，先安装
npm install -g pm2
```

### 2. 上传文件到服务器
```bash
# 将以下文件上传到 /home/<USER>/ 目录：
# - ecosystem.config.js
# - pm2_manager.sh  
# - brc20.py
# - config.json
```

### 3. 设置执行权限
```bash
cd /home/<USER>
chmod +x pm2_manager.sh
```

### 4. 启动服务
```bash
# 使用管理脚本启动
./pm2_manager.sh start

# 或直接使用 PM2
pm2 start ecosystem.config.js
pm2 save
```

### 5. 验证服务
```bash
# 查看服务状态
./pm2_manager.sh status

# 查看实时日志
./pm2_manager.sh logs

# 测试 API
curl http://localhost:5001/api/brc20/trending?limit=5
```

## 📋 常用命令

### PM2 管理脚本命令
```bash
./pm2_manager.sh start      # 启动服务
./pm2_manager.sh stop       # 停止服务
./pm2_manager.sh restart    # 重启服务
./pm2_manager.sh reload     # 重载服务（无停机重启）
./pm2_manager.sh status     # 查看状态
./pm2_manager.sh logs       # 查看日志
./pm2_manager.sh monitor    # 监控面板
./pm2_manager.sh delete     # 删除服务
```

### 直接 PM2 命令
```bash
pm2 list                    # 列出所有进程
pm2 show brc20_api         # 显示详细信息
pm2 logs brc20_api         # 查看日志
pm2 restart brc20_api      # 重启服务
pm2 stop brc20_api         # 停止服务
pm2 delete brc20_api       # 删除服务
pm2 monit                  # 监控面板
```

## 🔧 systemd 部署（备选）

如果不想使用 PM2，可以使用 systemd：

### 1. 复制服务文件
```bash
sudo cp brc20_api.service /etc/systemd/system/
```

### 2. 启用服务
```bash
sudo systemctl daemon-reload
sudo systemctl enable brc20_api
sudo systemctl start brc20_api
```

### 3. 管理服务
```bash
sudo systemctl status brc20_api    # 查看状态
sudo systemctl restart brc20_api   # 重启服务
sudo systemctl stop brc20_api      # 停止服务
sudo journalctl -u brc20_api -f    # 查看日志
```

## 📊 监控和日志

### PM2 日志位置
- 错误日志: `/home/<USER>/logs/err.log`
- 输出日志: `/home/<USER>/logs/out.log`
- 合并日志: `/home/<USER>/logs/combined.log`

### systemd 日志
```bash
# 查看实时日志
sudo journalctl -u brc20_api -f

# 查看最近日志
sudo journalctl -u brc20_api --since "1 hour ago"
```

## 🔒 安全配置

### 防火墙设置
```bash
# 开放 API 端口（如果需要外部访问）
sudo ufw allow 5001

# 或限制特定 IP 访问
sudo ufw allow from YOUR_IP to any port 5001
```

### nginx 反向代理（可选）
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location /api/ {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 🚨 故障排除

### 常见问题

1. **虚拟环境问题**
   ```bash
   # 确保虚拟环境路径正确
   ls -la /home/<USER>/venv/bin/python
   ```

2. **端口被占用**
   ```bash
   # 检查端口占用
   sudo netstat -tlnp | grep 5001
   ```

3. **权限问题**
   ```bash
   # 设置正确的权限
   sudo chown -R root:root /home/<USER>
   chmod +x /home/<USER>/venv/bin/python
   ```

4. **依赖包问题**
   ```bash
   # 重新安装依赖
   cd /home/<USER>
   source venv/bin/activate
   pip install -r requirements.txt
   ```

### 性能优化

- 内存限制: 1GB（可在配置中调整）
- 自动重启: 3秒延迟
- 日志轮转: PM2 自动管理
- 缓存清理: 自动清理旧区块缓存

## 📈 监控指标

- API 响应时间
- 内存使用量
- 缓存命中率
- RPC 调用频率
- 错误率

使用 `pm2 monit` 可以实时查看这些指标。 