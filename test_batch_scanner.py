#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量区块扫描脚本
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from batch_block_scanner import *

def test_witness_conditions():
    """测试witness条件检查"""
    print("=== 测试witness条件检查 ===")
    
    # 测试长度不足的witness
    short_witness = "a" * 500
    result1 = check_witness_conditions(short_witness)
    print(f"短witness (500字符): {result1}")  # 应该是False
    
    # 测试长度足够但不包含目标字符串的witness
    long_witness_no_target = "a" * 1500
    result2 = check_witness_conditions(long_witness_no_target)
    print(f"长witness但无目标字符串: {result2}")  # 应该是False
    
    # 测试长度足够且包含目标字符串的witness
    long_witness_with_target = "a" * 500 + TARGET_STRING + "b" * 500
    result3 = check_witness_conditions(long_witness_with_target)
    print(f"长witness且包含目标字符串: {result3}")  # 应该是True
    
    print()

def test_address_extraction():
    """测试地址提取"""
    print("=== 测试地址提取 ===")
    
    # 模拟交易数据结构
    mock_tx = {
        "txid": "test_txid",
        "vin": [{
            "txid": "prev_txid",
            "vout": 0,
            # 模拟有prevout信息的情况
            "prevout": {
                "scriptpubkey_address": "bc1peve9pqv3k9hwfc57pu8mnjtdlyldp8jha57dgvqc3kexvrk7r43qlyqgsz"
            }
        }],
        "vout": [{
            "value": 0.00001,
            "scriptPubKey": {
                "address": "bc1pxwu6adzrdp8y7g5a2ywsv9jwewdjnhpck367tcxrtjcmyf4a78hs5hfjzx",
                "type": "witness_v1_taproot"
            }
        }]
    }
    
    mint_addr, receive_addr, need_query = extract_addresses_from_transaction(mock_tx)
    print(f"铸造地址: {mint_addr}")
    print(f"收货地址: {receive_addr}")
    print(f"需要查询: {need_query}")
    print()

def test_config_loading():
    """测试配置加载"""
    print("=== 测试配置加载 ===")
    
    try:
        config = load_config()
        print(f"RPC主机: {config['bitcoin_node']['rpc_host']}")
        print(f"RPC端口: {config['bitcoin_node']['rpc_port']}")
        print(f"RPC用户: {config['bitcoin_node']['rpc_user']}")
        print("配置加载成功")
    except Exception as e:
        print(f"配置加载失败: {e}")
    print()

def test_rpc_connection():
    """测试RPC连接"""
    print("=== 测试RPC连接 ===")
    
    try:
        # 尝试获取当前区块高度
        height = getblockcount()
        if height:
            print(f"当前区块高度: {height}")
            print("RPC连接正常")
        else:
            print("RPC连接失败：无法获取区块高度")
    except Exception as e:
        print(f"RPC连接异常: {e}")
    print()

def main():
    """主测试函数"""
    print("批量区块扫描脚本测试")
    print("=" * 50)
    
    # 运行各项测试
    test_witness_conditions()
    test_address_extraction()
    test_config_loading()
    test_rpc_connection()
    
    print("测试完成")
    print("=" * 50)
    
    # 如果RPC连接正常，可以尝试处理一个小范围的区块
    try:
        height = getblockcount()
        if height and height > 0:
            print(f"\n当前区块高度: {height}")
            
            # 询问是否要测试处理单个区块
            response = input("是否要测试处理最新区块？(y/n): ")
            if response.lower() == 'y':
                print(f"正在测试处理区块 {height}...")
                records = process_block(height)
                print(f"在区块 {height} 中找到 {len(records)} 笔符合条件的交易")
                
                if records:
                    print("找到的交易:")
                    for i, record in enumerate(records[:3], 1):  # 只显示前3笔
                        print(f"  {i}. {record.txid}")
                        print(f"     铸造地址: {record.mint_address}")
                        print(f"     收货地址: {record.receive_address}")
    except Exception as e:
        print(f"测试处理区块时出错: {e}")

if __name__ == "__main__":
    main()
