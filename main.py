# -*- coding: utf-8 -*-
import time
import requests
import json
import alkanes
import threading
import logging
import re
import os

from bitcoin.core import CM<PERSON><PERSON><PERSON>ransaction, b2x
from bitcoin.core.script import  OP_RETURN
from fastapi import FastAPI, Path, Query, HTTPException
from pydantic import BaseModel
from typing import Union, List, Optional
import numpy as np
import math
from uvicorn.config import Config
from uvicorn.main import Server

from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from starlette.middleware.httpsredirect import HTTPSRedirectMiddleware

# 连接本地比特币节点
rpc_user = 'xiamms'
rpc_password = '77534272'
rpc_host = '************'
rpc_port = '8082'
rpc_url = f'http://{rpc_host}:{rpc_port}'
current_id = 1
stop_signal = threading.Event()

# 将RUNE_URL替换为新的API地址
ALKANES_API_URL = 'https://alkanes-api.idclub.io/token/page'
ALKANES_CONFIG_URL = 'https://alkanes-api.idclub.io/config'

app = FastAPI()

# GZip 压缩
app.add_middleware(GZipMiddleware, minimum_size=1000)
# Deflate 压缩（实际上是通过 GZipMiddleware 实现的）
app.add_middleware(GZipMiddleware, minimum_size=1000, compresslevel=1)

rune_cache = {}
block_cache = {}
recent_blocks = 5  # 将20改为5，只追踪最近5个区块
scanned_blocks = set()  # 新增：记录已扫描过的区块
# 全局变量,用于存储符文信息
rune_info = {}
update_timestamps = {}
# 配置变量,设置最大数据量
MAX_RUNE_INFO_COUNT = 500

# 无效符文id
invalid_rune_ids = set()
# 非符文ids
old_txids_general = set()

# 全局变量，存储所有Alkanes代币信息
alkanes_token_cache = {}
# 记录最后一次API更新时间
last_api_update_time = 0
# API更新间隔(秒)
API_UPDATE_INTERVAL = 120  # 改为120秒更新一次
# 当检测到新区块时的API更新提前时间(秒)
NEW_BLOCK_API_UPDATE_INTERVAL = 30  # 检测到新区块后30秒进行API更新

# 添加新的全局变量
current_update_height = 0  # 记录当前缓存的区块高度
last_processed_height = 0  # 记录最后一次处理过的区块高度用于增量更新
temp_token_cache = {}  # 临时存储区块扫描后的代币数据更新

# 将最大保存区块数改为150
MAX_RECENT_BLOCKS = 150  # 最多保留150个区块数据
recent_blocks = 5  # 默认扫描5个区块

# 添加一个请求级别的缓存机制
_request_update_timestamp = 0  # 记录每个请求中最后一次更新尝试的时间

class RuneInfo(BaseModel):
    rune_id: str = None
    txid: str
    fee: float
    output_address: Optional[str] = None

class TxidRank(BaseModel):
    txid: str
    rank: int

class RuneSummary(BaseModel):
    rune_id: str
    total_transactions: int
    unique_holders: int
    fee_distribution: dict
    median_fee_rate: float
    rune_info: dict
    pperchain: Optional[int] = None  # 添加新字段，默认为None
    pperchain_fee: Optional[float] = None  # 添加费率下限字段

# 配置日志记录器
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 封装一个日志打印函数
def log_info(*args):
    message = ' '.join(str(arg) for arg in args)
    logging.info(message)

def log_warning(*args):
    message = ' '.join(str(arg) for arg in args)
    logging.warning(message)

def log_error(*args):
    message = ' '.join(str(arg) for arg in args)
    logging.error(message)

@app.on_event("startup")
async def startup_event():
    log_info("应用程序启动")
    
    # 先加载本地文件数据
    load_data_from_file()
    
    # 尝试从本地缓存加载Alkanes代币信息
    if load_alkanes_cache_from_file():
        log_info("已成功从本地缓存加载代币信息")
    else:
        log_info("本地缓存加载失败，尝试从API更新")
        # 初始化加载Alkanes代币信息
        update_alkanes_token_info()
    
    # 在这里执行应用程序启动时的其他操作

@app.on_event("shutdown")
async def shutdown_event():
    log_info("准备结束进程")
    stop_signal.set()
    listener_thread.join()
    block_scanner_thread.join()  # 添加等待区块扫描线程结束
    # 不需要等待cache_thread，因为它是守护线程

    # 保存所有数据
    save_data_to_file()
    # 如果有最新的API数据，确保保存
    if alkanes_token_cache:
        api_data = {
            "code": 0,
            "msg": "ok",
            "data": {
                "alkanesList": list(alkanes_token_cache.values()),
                "updateHeight": str(current_update_height)
            }
        }
        save_alkanes_api_cache(api_data)

    log_info("应用程序关闭")

def shutdown_save_data():
    log_info("准备提取交易信息进行保存")
    # 从 rune_cache 提取符文信息
    rune_transactions = []
    for rune_id, txs in rune_cache.items():
        for txid, tx_info in txs.items():
            rune_transactions.append({
                "txid": txid,
                "rune_id": rune_id.split('-')[1],
                "output_address": tx_info['output_address']
            })

    # 保存符文交易信息
    save_rune_transactions('old_rune_transactions.json', rune_transactions)
    log_info(f"保存了 {len(rune_transactions)} 笔符文交易")

    # 获取最新的内存池交易,由于rune_cache并没有进行最后一次获取,所以有一定概率会丢失小部分没统计到的新增符文
    new_mempool_txids = get_mempool_transactions()

    # 分离出普通交易
    rune_txids = set(tx['txid'] for tx in rune_transactions)
    normal_txids = new_mempool_txids - rune_txids

    # 保存普通交易信息
    save_data_to_file('old_txids_general', list(normal_txids))
    log_info(f"保存了 {len(normal_txids)} 笔普通交易")

def rpc_call(method,params=[]):
    query = [
        {
            "id": current_id,
            "jsonrpc": "2.0",
            "method": method,
            "params": params
        }]
    responses = rpc_batch(query)
    for i, resp in enumerate(responses):
        data = resp['result']
        return data
    
def getblockcount():
    max_retries = 3
    retry_delay = 5  # 秒
    
    for attempt in range(max_retries):
        try:
            blockHeight = rpc_call('getblockcount')
            if blockHeight is not None:
                return blockHeight
            
            log_error(f"获取区块高度返回None (尝试 {attempt + 1}/{max_retries})")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue
                
        except Exception as e:
            log_error(f"获取区块高度出错 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue
    
    return 0  # 返回0而不是None，作为安全的默认值

def getblockhash(blockHeight):
    blockhash = rpc_call('getblockhash',[blockHeight])
    return blockhash

# 获取区块交易
def getblock(blockhash):
    data = rpc_call('getblock',[blockhash,2])
    return data

def scanRuneBlock(blockHeight):
    global block_cache
    log_info(f"开始扫描区块:{blockHeight}")
    try:
        blockhash = getblockhash(blockHeight)
        if not blockhash:
            log_error(f"无法获取区块 {blockHeight} 的哈希")
            return False
            
        rune_transactions = []
        block_data = getblock(blockhash)
        if not block_data:
            log_error(f"无法获取区块 {blockHeight} 的数据")
            return False
            
        txs = block_data["tx"]
        for tx in txs:
            for vout in tx['vout']:
                asm = vout['scriptPubKey']['asm']
                if asm.startswith("OP_RETURN 13"):
                    hex_data = vout['scriptPubKey']['hex']
                    if hex_data.startswith("6a"):
                        op_return_hex = hex_data[2:]  # 跳过前面的 '6a'
                        pushdata_len = int(op_return_hex[:2], 16)  # OP_PUSHDATA1 长度
                        rune_data = op_return_hex[2+2:2+2+pushdata_len*2]  # 提取数据
                        rune_id = analyze_alkanes(rune_data)
                        if rune_id:
                            # 从返回的数据中提取output_address
                            output_address = None
                            for vout in tx['vout']:
                                if 'address' in vout['scriptPubKey']:
                                    output_address = vout['scriptPubKey']['address']
                                    #break #不跳过筛选到的就是找零地址
                            rune_transactions.append({
                                "txid": tx["txid"],
                                "rune_id": rune_id,
                                "fee": int(tx["fee"] * 100000000),
                                "output_address": output_address
                            })
        # 无论是否有符文交易,都将区块高度添加到 block_cache 中
        block_cache[blockHeight] = {}
        if rune_transactions:
            for tx in rune_transactions:
                rune_id = tx['rune_id']
                txid = tx['txid']
                fee = tx['fee']
                output_address = tx['output_address']
                block_cache[blockHeight].setdefault(rune_id, {})[txid] = {
                    'fee': fee,
                    'output_address': output_address
                }
        return True  # 扫描成功
    except Exception as e:
        log_error(f"扫描区块 {blockHeight} 时出错: {str(e)}")
        return False  # 扫描失败

def update_rune_info_after_scan(blockHeight):
    global rune_info, block_cache, update_timestamps
    
    # 从block_cache中提取当前区块的符文ID
    rune_ids = set()
    if blockHeight in block_cache:
        rune_ids = set(str(rune_id) for rune_id in block_cache[blockHeight].keys())
    
    # 使用新的API更新所有代币信息
    update_alkanes_token_info()
    
    # 更新时间戳
    current_time = time.time()
    for rune_id in rune_ids:
        if rune_id in alkanes_token_cache:
            update_timestamps[rune_id] = current_time
        else:
            # 不再自动添加到无效ID列表，只记录日志
            log_warning(f"区块扫描发现代币 {rune_id} 不在缓存中，可能是新代币")
    
    # 将更新后的符文信息保存到文件中
    clean_expired_rune_info()  # 清理过期数据
    save_data_to_file()

# 从URL获取HTML内容
def get_html(url):
    # 此函数已不再需要,但保留为向后兼容
    log_warning("get_html函数已废弃")
    return "{}"

# 使用正则表达式提取数据并过滤符号
def parse_with_regex(html_content):
    # 此函数已不再需要,但保留为向后兼容
    log_warning("parse_with_regex函数已废弃")
    return {}

# 将数据转换为JSON格式并返回
def get_rune_data_form_ord(runeid):
    """
    从缓存中获取代币数据
    
    参数:
        runeid (str): 代币ID
        
    返回:
        str: JSON格式的代币数据
    """
    # 确保先更新代币缓存
    update_alkanes_token_info()
    
    if runeid in alkanes_token_cache:
        return json.dumps(alkanes_token_cache[runeid], indent=2, ensure_ascii=False)
    
    raise ValueError(f"Invalid token ID: {runeid}")

def get_rune_id_from_ord(input):
    """
    从缓存中通过名称查找代币ID
    
    参数:
        input (str): 代币名称或ID
    
    返回:
        str: 代币ID或None
    """
    # 如果输入格式已是ID格式,直接返回
    parts = input.split(':')
    if len(parts) == 2:
        return input
    
    # 先确保代币缓存是最新的
    update_alkanes_token_info()
    
    # 在缓存中查找匹配名称的代币
    for token_id, token_data in alkanes_token_cache.items():
        if token_data.get('name', '').lower() == input.lower() or token_data.get('symbol', '').lower() == input.lower():
            return token_id
    
    return None

# 从文件加载符文信息和无效的符文ID到内存
def load_data_from_file():
    global rune_info, invalid_rune_ids,update_timestamps,old_txids_general
    if os.path.exists('rune_info.json'):
        with open('rune_info.json', 'r', encoding='utf-8') as f:
            rune_info = json.load(f)
    if os.path.exists('invalid_rune_ids.json'):
        with open('invalid_rune_ids.json', 'r', encoding='utf-8') as f:
            invalid_rune_ids = set(json.load(f))
    if os.path.exists('update_timestamps.json'):
        with open('update_timestamps.json', 'r', encoding='utf-8') as f:
            update_timestamps = json.load(f)
    if os.path.exists('old_txids_general.json'):
        with open('old_txids_general.json', 'r', encoding='utf-8') as f:
            old_txids_general = set(json.load(f))

# 将符文信息和无效的符文ID保存到文件
def save_data_to_file(filename = None,data = None):
    if filename:
        with open(f'{filename}.json', 'w', encoding='utf-8') as f:
            json.dump(list(data), f, indent=2, ensure_ascii=False)
    else:
        with open('rune_info.json', 'w', encoding='utf-8') as f:
            json.dump(rune_info, f, indent=2, ensure_ascii=False)
        with open('invalid_rune_ids.json', 'w', encoding='utf-8') as f:
            json.dump(list(invalid_rune_ids), f, indent=2, ensure_ascii=False)
        with open('update_timestamps.json', 'w', encoding='utf-8') as f:
            json.dump(update_timestamps, f, indent=2, ensure_ascii=False)


def check_and_get_alkanes_info(token_id):
    """
    检查并获取Alkanes代币信息，替代原来的check_and_get_rune_info函数
    
    参数:
        token_id (str): Alkanes代币ID
        
    返回:
        dict: 代币信息或None(如果ID无效)
    """
    global alkanes_token_cache, invalid_rune_ids
    
    if token_id in invalid_rune_ids:
        return None
        
    # 确保缓存是最新的
    update_alkanes_token_info()
    
    if token_id in alkanes_token_cache:
        return alkanes_token_cache[token_id]
    
    # 如果缓存中没有该ID，可能是新代币或无效ID
    # 不再自动添加到无效ID列表，需要手动管理 invalid_rune_ids.json
    log_warning(f"代币 {token_id} 不在缓存中，可能是新代币或无效ID")
    return None

def clean_expired_rune_info():
    global rune_info, update_timestamps
    
    if len(rune_info) > MAX_RUNE_INFO_COUNT:
        # 根据更新时间戳对符文信息进行排序
        sorted_rune_ids = sorted(rune_info.keys(), key=lambda x: update_timestamps.get(x, 0))
        
        # 计算需要清理的数据量
        #clean_count = len(rune_info) - MAX_RUNE_INFO_COUNT
        clean_count = len(rune_info) - 100 #清理100条就可以了
        # 清理过期数据
        for rune_id in sorted_rune_ids[:clean_count]:
            del rune_info[rune_id]
            if rune_id in update_timestamps:
                del update_timestamps[rune_id]
        
        log_info(f"Cleaned {clean_count} expired rune info")

@app.get("/api/rune/recent")
async def get_recent_rune_data(limit: int = Query(6, description="返回最近的区块数量")):
    try:
        recent_blocks = sorted(block_cache.keys(), reverse=True)[:limit]
        result = []

        for block_height in recent_blocks:
            block_data = block_cache[block_height]
            rune_stats = {}
            rune_count = 0

            # 复制 block_data 的项以安全地迭代
            block_data_items = list(block_data.items())

            for rune_id, tx_data in block_data_items:
                rune_id_str = str(rune_id)
                if rune_id_str not in rune_stats:
                    rune_stats[rune_id_str] = {
                        "tx_count": 0,
                        "addresses": set()
                    }
                
                # 复制 tx_data 的项以安全地迭代
                tx_data_items = list(tx_data.items())
                
                for tx_id, tx_info in tx_data_items:
                    rune_stats[rune_id_str]["tx_count"] += 1
                    if "output_address" in tx_info:
                        rune_stats[rune_id_str]["addresses"].add(tx_info["output_address"])
                
                rune_count += 1

            # 转换为JSON可序列化格式
            rune_stats_result = []
            for rune_id, stats in rune_stats.items():
                rune_name = ""
                # 从缓存中获取代币名称
                if rune_id in alkanes_token_cache:
                    rune_name = alkanes_token_cache[rune_id].get("name", "")
                rune_stats_result.append({
                    "rune_id": rune_id,
                    "rune_name": rune_name,
                    "tx_count": stats["tx_count"],
                    "address_count": len(stats["addresses"])
                })

            result.append({
                "block_height": block_height,
                "rune_count": rune_count,
                "rune_stats": rune_stats_result
            })

        return {"recent_blocks": result}

    except Exception as e:
        log_error(f"查询最近的区块出错: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

@app.get("/api/rune/{block_height}")
async def get_rune_data(block_height: int = Path(..., description="区块高度")):
    try:
        # 如果区块不在缓存中，尝试按需扫描
        if block_height not in block_cache:
            log_info(f"按需扫描区块: {block_height}")
            try:
                # 直接调用扫描函数，获取成功或失败的结果
                success = scanRuneBlock(block_height)
                if not success:
                    raise HTTPException(status_code=404, detail=f"无法扫描区块 {block_height}")
                
                # 更新符文信息
                update_rune_info_after_scan(block_height)
                
                # 如果扫描后仍不在缓存中，可能区块中没有Alkanes交易
                if block_height not in block_cache:
                    log_info(f"区块 {block_height} 扫描成功但没有Alkanes交易")
                    # 创建一个空记录，避免下次再扫描
                    block_cache[block_height] = {}
            except Exception as e:
                log_error(f"按需扫描区块 {block_height} 失败: {str(e)}")
                raise HTTPException(status_code=404, detail=f"区块高度 {block_height} 不存在或无法扫描")
        
        log_info(f"查询区块:{block_height}")
        
        block_data = block_cache[block_height]
        rune_stats = {}
        rune_count = 0

        # 复制 block_data 的项以安全地迭代
        block_data_items = list(block_data.items())

        for rune_id, tx_data in block_data_items:
            rune_id_str = str(rune_id)
            if rune_id_str not in rune_stats:
                rune_stats[rune_id_str] = {
                    "tx_count": 0,
                    "addresses": set()
                }
            
            # 复制 tx_data 的项以安全地迭代
            tx_data_items = list(tx_data.items())
            
            for tx_id, tx_info in tx_data_items:
                rune_stats[rune_id_str]["tx_count"] += 1
                if "output_address" in tx_info:
                    rune_stats[rune_id_str]["addresses"].add(tx_info["output_address"])
            
            rune_count += 1

        # 转换为JSON可序列化格式
        rune_stats_result = []
        for rune_id, stats in rune_stats.items():
            rune_name = ""
            # 从缓存中获取代币名称
            if rune_id in alkanes_token_cache:
                rune_name = alkanes_token_cache[rune_id].get("name", "")
            rune_stats_result.append({
                "rune_id": rune_id,
                "rune_name": rune_name,
                "tx_count": stats["tx_count"],
                "address_count": len(stats["addresses"])
            })

        return {
            "block_height": block_height,
            "rune_count": rune_count,
            "rune_stats": rune_stats_result
        }
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"查询区块 {block_height} 出错: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")


def generate_intervals(fees, max_intervals=6):
    if not fees:
        return []
    unique_fees = sorted(set(fees))
    if len(unique_fees) == 1:
        return [(unique_fees[0], unique_fees[0] + 1)]
    
    min_fee, max_fee = unique_fees[0], unique_fees[-1]
    interval_count = min(max_intervals, len(unique_fees))
    
    if interval_count == len(unique_fees):
        return [(fee, next_fee) for fee, next_fee in zip(unique_fees, unique_fees[1:] + [unique_fees[-1] + 1])]
    
    intervals = []
    start = min_fee
    for i in range(interval_count - 1):
        end = min_fee + (max_fee - min_fee) * (i + 1) // (interval_count - 1)
        intervals.append((start, end))
        start = end
    
    intervals.append((start, max_fee + 1))
    
    return intervals

@app.get('/api/runes/summary/hotMints')
async def get_hot_mints(limit: Optional[int] = Query(10, ge=1, description="Number of top mints to return")):
    start_time = time.time()
    rune_summaries = []

    # 复制 rune_cache 的项以安全地迭代
    rune_cache_items = list(rune_cache.items())

    for rune_id, rune_data in rune_cache_items:
        rune_id_str = rune_id.split('-')[1]
        if rune_id_str in invalid_rune_ids:
            continue
            
        # 使用新的函数获取代币信息
        alkanes_info = check_and_get_alkanes_info(rune_id_str)
        if alkanes_info is None or not (alkanes_info.get('mintActive', 0) == 1 and alkanes_info.get('actualMintActive', 0) == 1):
            continue

        total_transactions = len(rune_data)

        # 复制 rune_data 的项以安全地迭代
        rune_data_items = list(rune_data.items())

        fees = [data['fee'] for _, data in rune_data_items]
        addresses = set(data['output_address'] for _, data in rune_data_items)
        unique_holders = len(addresses)
        median_fee_rate = float(np.median(fees))
        
        # 使用自适应区间划分算法生成区间,最多6个区间
        intervals = generate_intervals(fees)
        
        # 计算每个区间内的交易数量
        fee_distribution = {}
        for low, high in intervals:
            count = sum(1 for f in fees if low <= f < high)
            if count > 0:
                fee_distribution[f"{int(low)}-{int(high)}"] = count
        
        # 转换新API数据格式为旧格式
        converted_rune_info = {
            "name": alkanes_info.get("name", ""),
            "number": alkanes_info.get("id", "").split(":")[-1],
            "id": alkanes_info.get("id", ""),
            "start": None,
            "end": None,
            "amount": str(alkanes_info.get("mintAmount", 0)),
            "mints": str(alkanes_info.get("minted", 0)),
            "cap": str(alkanes_info.get("cap", 0)),
            "remaining": str(int(float(alkanes_info.get("cap", 0))) - int(float(alkanes_info.get("minted", 0)))),
            "mintable": "true" if (alkanes_info.get("mintActive", 0) == 1 and alkanes_info.get("actualMintActive", 0) == 1) else "false",
            "supply": str(int(float(alkanes_info.get("mintAmount", 0))) * int(float(alkanes_info.get("cap", 0)))),
            "mint_progress": f"{alkanes_info.get('progress', 0)}%",
            "premine": str(alkanes_info.get("premine", 0)),
            "premine_percentage": calculate_premine_percentage(alkanes_info),
            "symbol": alkanes_info.get("symbol", ""),
            "updateHeight": current_update_height
        }
        
        rune_summaries.append(RuneSummary(
            rune_id=rune_id_str,
            total_transactions=total_transactions,
            unique_holders=unique_holders,
            fee_distribution=fee_distribution,
            median_fee_rate=median_fee_rate,
            rune_info=converted_rune_info
        ))

    rune_summaries.sort(key=lambda x: x.total_transactions, reverse=True)
    hot_mints = rune_summaries[:limit]

    end_time = time.time()
    query_time = end_time - start_time
    print(f"Query time: {query_time:.4f} seconds")
    return hot_mints


@app.get('/api/runes/summary', response_model=List[RuneSummary])
async def get_runes_summary(rune_id: Optional[str] = None):
    start_time = time.time()
    rune_summaries = []

    if rune_id:
        if rune_id in invalid_rune_ids:
            return {"error": "无效id"}
            
        # 尝试通过名称获取ID
        if ":" not in rune_id:
            rune_id = get_token_id_by_name(rune_id)
            if rune_id is None:
                return {"error": "无效id"}
                
        rune_data = rune_cache.get(f"rune-{rune_id}", {})
        if rune_data:
            # 使用新的函数获取代币信息
            alkanes_info = check_and_get_alkanes_info(rune_id)
            total_transactions = len(rune_data)
            
            # 复制 rune_data 的项以安全地迭代
            rune_data_items = list(rune_data.items())
            
            fees = [data['fee'] for _, data in rune_data_items]
            addresses = set(data['output_address'] for _, data in rune_data_items)
            unique_holders = len(addresses)
            median_fee_rate = float(np.median(fees))

            # 使用自适应区间划分算法生成区间，最多6个区间
            intervals = generate_intervals(fees)

            # 计算每个区间内的交易数量
            fee_distribution = {}
            for low, high in intervals:
                count = sum(1 for f in fees if low <= f < high)
                if count > 0:
                    fee_distribution[f"{int(low)}-{int(high)}"] = count

            # 转换新API数据格式为旧格式
            converted_rune_info = {
                "name": alkanes_info.get("name", ""),
                "number": alkanes_info.get("id", "").split(":")[-1],
                "id": alkanes_info.get("id", ""),
                "start": None,
                "end": None,
                "amount": str(alkanes_info.get("premine", 0)),
                "mints": str(alkanes_info.get("minted", 0)),
                "cap": str(alkanes_info.get("cap", 0)),
                "remaining": str(int(float(alkanes_info.get("cap", 0))) - int(float(alkanes_info.get("minted", 0)))),
                "mintable": "true" if (alkanes_info.get("mintActive", 0) == 1 and alkanes_info.get("actualMintActive", 0) == 1) else "false",
                "supply": str(int(float(alkanes_info.get("mintAmount", 0))) * int(float(alkanes_info.get("cap", 0)))),
                "mint_progress": f"{alkanes_info.get('progress', 0)}%",
                "premine": str(alkanes_info.get("premine", 0)),
                "premine_percentage": calculate_premine_percentage(alkanes_info),
                "symbol": alkanes_info.get("symbol", ""),
                "updateHeight": current_update_height
            }

            rune_summaries.append(RuneSummary(
                rune_id=rune_id,
                total_transactions=total_transactions,
                unique_holders=unique_holders,
                fee_distribution=fee_distribution,
                median_fee_rate=median_fee_rate,
                rune_info=converted_rune_info
            ))
    else:
        # 复制 rune_cache 的项以安全地迭代
        rune_cache_items = list(rune_cache.items())

        for rune_id, rune_data in rune_cache_items:
            rune_id_str = rune_id.split('-')[1]
            total_transactions = len(rune_data)
            if total_transactions < 20:
                continue
            if rune_id_str in invalid_rune_ids:
                continue
            
            # 使用新的函数获取代币信息
            alkanes_info = check_and_get_alkanes_info(rune_id_str)
            if alkanes_info is None or not (alkanes_info.get('mintActive', 0) == 1 and alkanes_info.get('actualMintActive', 0) == 1):
                continue

            # 复制 rune_data 的项以安全地迭代
            rune_data_items = list(rune_data.items())

            fees = [data['fee'] for _, data in rune_data_items]
            addresses = set(data['output_address'] for _, data in rune_data_items)
            unique_holders = len(addresses)
            median_fee_rate = float(np.median(fees)) if fees else 0.0

            # 使用自适应区间划分算法生成区间，最多6个区间
            intervals = generate_intervals(fees)

            # 计算每个区间内的交易数量
            fee_distribution = {}
            for low, high in intervals:
                count = sum(1 for f in fees if low <= f < high)
                if count > 0:
                    fee_distribution[f"{int(low)}-{int(high)}"] = count

            # 转换新API数据格式为旧格式
            converted_rune_info = {
                "name": alkanes_info.get("name", ""),
                "number": alkanes_info.get("id", "").split(":")[-1],
                "id": alkanes_info.get("id", ""),
                "start": None,
                "end": None,
                "amount": str(alkanes_info.get("premine", 0)),
                "mints": str(alkanes_info.get("minted", 0)),
                "cap": str(alkanes_info.get("cap", 0)),
                "remaining": str(int(float(alkanes_info.get("cap", 0))) - int(float(alkanes_info.get("minted", 0)))),
                "mintable": "true" if (alkanes_info.get("mintActive", 0) == 1 and alkanes_info.get("actualMintActive", 0) == 1) else "false",
                "supply": str(int(float(alkanes_info.get("mintAmount", 0))) * int(float(alkanes_info.get("cap", 0)))),
                "mint_progress": f"{alkanes_info.get('progress', 0)}%",
                "premine": str(alkanes_info.get("premine", 0)),
                "premine_percentage": calculate_premine_percentage(alkanes_info),
                "symbol": alkanes_info.get("symbol", ""),
                "updateHeight": current_update_height
            }

            rune_summaries.append(RuneSummary(
                rune_id=rune_id_str,
                total_transactions=total_transactions,
                unique_holders=unique_holders,
                fee_distribution=fee_distribution,
                median_fee_rate=median_fee_rate,
                rune_info=converted_rune_info
            ))

    end_time = time.time()
    query_time = end_time - start_time
    print(f"Query time: {query_time:.4f} seconds")
    return rune_summaries


@app.get('/api/runes/{rune_id}', response_model=Union[List[RuneInfo], TxidRank], response_model_exclude_unset=True)
async def get_rune_transactions(
    rune_id: str = Path(..., title="The ID of the rune to get"),
    limit: int = Query(0, ge=0, description="Number of transactions to return. 0 means return all."),
    target_txid: Optional[str] = Query(None, description="The txid to find the rank for")
):
    start_time = time.time()
    rune_data = rune_cache.get(f"rune-{rune_id}", {})
    if not rune_data:
        return []
    
    # 复制 rune_data 的项以安全地迭代
    rune_data_items = list(rune_data.items())
    
    rune_info = []
    for txid, details in rune_data_items:
        rune_info.append(RuneInfo(rune_id=rune_id, txid=txid, fee=details['fee'], output_address=details['output_address']))
    
    rune_info.sort(key=lambda x: x.fee, reverse=True)

    if target_txid:
        rank = next((i for i, x in enumerate(rune_info) if x.txid == target_txid), None)
        if rank is not None:
            end_time = time.time()
            query_time = end_time - start_time
            print(f"Query time: {query_time:.4f} seconds")
            return TxidRank(txid=target_txid, rank=rank + 1)
        else:
            raise HTTPException(status_code=404, detail=f"Transaction {target_txid} not found in rune {rune_id}")

    if limit > 0:
        rune_info = rune_info[:limit]

    end_time = time.time()
    query_time = end_time - start_time
    print(f"Query time: {query_time:.4f} seconds")
    return rune_info


def rpc_batch(requests_list):
    """
    执行批量RPC调用
    :param requests_list: 包含多个请求字典的列表
    :return: 响应列表
    """
    global current_id  # 声明使用全局变量 current_id

    payload = json.dumps(requests_list)
    # 更新current_id为最后一个请求的id + 1
    current_id = requests_list[-1]['id'] + 1
    
    max_retries = 3
    retry_delay = 5  # 秒
    
    for attempt in range(max_retries):
        try:
            response = requests.post(rpc_url, auth=(rpc_user, rpc_password), data=payload)
            response_data = response.json()
            # 检查是否为列表响应
            if isinstance(response_data, list):
                return response_data
            else:
                log_error(f"RPC返回非列表响应: {response_data}")
                return None

        except requests.RequestException as e:
            log_error(f"RPC请求错误(尝试 {attempt + 1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue
            return None

        except json.JSONDecodeError as e:
            log_error(f"JSON解析错误(尝试 {attempt + 1}/{max_retries}): {str(e)}")
            if attempt < max_retries - 1:
                time.sleep(retry_delay)
                continue
            return None
    
    return None

# 获取当前内存池中的所有交易
def get_mempool_transactions():
    query = [
        {
            "id": current_id,
            "jsonrpc": "2.0",
            "method": "getrawmempool",
            "params": []
        }]
    responses = rpc_batch(query)
    if not responses:
        log_error("获取内存池交易失败")
        return set()  # 返回空集合而不是None
        
    for i, resp in enumerate(responses):
        if 'result' in resp:
            tx_data = resp['result']
            return set(tx_data)
        else:
            log_error(f"获取内存池交易响应错误: {resp.get('error', 'Unknown error')}")
            return set()
    
    return set()  # 以防万一没有处理到响应

# 批量查询交易详情
def get_transaction_details_batch(txids, batch_size=5000):
    tx_details = []
    for i in range(0, len(txids), batch_size):
        batch_txids = txids[i:i+batch_size]
        batch_query = [
            {
                "id": current_id + j,
                "jsonrpc": "2.0",
                "method": "getmempoolentry",
                "params": [txid]
            }
            for j, txid in enumerate(batch_txids)
        ]
        responses = rpc_batch(batch_query)
        for j, resp in enumerate(responses):
            if 'result' in resp:
                tx_data = resp['result']
                if tx_data is not None:  # 添加这个检查
                    fee_rate = calculate_fee_rate(tx_data)
                    tx_details.append({
                        'txid': batch_txids[j],
                        'fee': fee_rate,
                        'time': tx_data['time'],
                        'ancestorcount': tx_data['ancestorcount'],
                        'descendantcount': tx_data['descendantcount']
                    })
                else:
                    print(f"Warning: Transaction {batch_txids[j]} has no details.") #交易有可能已经被替换失效了
            else:
                print(f"Error fetching transaction details: {resp['error']}")
    return tx_details

def calculate_fee_rate(tx_data):
    total_fee = tx_data['fees']['ancestor'] + tx_data['fees']['descendant'] - tx_data['fees']['modified']
    total_size = tx_data['ancestorsize'] + tx_data['descendantsize'] - tx_data['vsize']
    #print(total_fee,total_size)
    return round(total_fee / total_size * 100000000, 2)

def get_mempool_ancestors(txids, batch_size=5000):
    all_ancestors = []
    for i in range(0, len(txids), batch_size):
        batch_txids = txids[i:i+batch_size]
        batch_query = [
            {
                "id": current_id + j,
                "jsonrpc": "2.0",
                "method": "getmempoolancestors",
                "params": [txid, True]
            }
            for j, txid in enumerate(batch_txids)
        ]
        responses = rpc_batch(batch_query)
        for resp in responses:
            if 'result' in resp:
                ancestors = resp['result']
                if ancestors is not None:
                    for ancestor_txid, ancestor_data in ancestors.items():
                        fee_rate = calculate_fee_rate(ancestor_data)
                        all_ancestors.append({
                            'txid': ancestor_txid,
                            'fee': fee_rate,
                            'time': ancestor_data['time']
                        })
                else:
                    print(f"Transaction {resp['id']} has no ancestors.")
            else:
                print(f"Error fetching mempool ancestors: {resp['error']}")
    
    return all_ancestors

def parse_transaction(tx_hex: str) -> dict:
    try:
        # Deserialize the hex string to a CTransaction object
        tx = CMutableTransaction.deserialize(bytes.fromhex(tx_hex))
        # Iterate through each transaction output
        for output in tx.vout:
            script = output.scriptPubKey  # This is the CScript object
            script_items = list(script)  # Convert script to a list to work with
            # Check if the script matches the pattern [OP_RETURN, 13, x(hex_data)]
            if len(script_items) >= 3 and script_items[0] == OP_RETURN and script_items[1] == 13:
                rune_data = b2x(script_items[2])  # Convert the binary data to a hex string
                
                return {"rune_data": rune_data}  # Return the first valid OP_RETURN data and input address
        
        return {}  # Return an empty dictionary if no valid OP_RETURN data is found
    except Exception as e:
        print(str(e))
        return {}  # Return an empty dictionary in case of an error

def analyze_alkanes(rune_hex):
    """
    解析 Runestone hex，查找 Alkanes 协议 (ID=1) 的 MINT 操作，
    并返回对应的 Alkane ID。
    """
    try:
        problematic_data_bytes = bytes.fromhex(rune_hex)
        problematic_integers = alkanes.decode_leb128(problematic_data_bytes)
        rune_data = alkanes.parse_runestones(problematic_integers)
        # 检查 protostones 列表
        if rune_data and hasattr(rune_data, 'protostones') and rune_data.protostones:
            for protostone in rune_data.protostones:
                # 检查 Protocol ID 是否为 1 (Alkanes)
                if protostone.protocol_id == 1:
                    alkane_call = getattr(protostone, 'alkane_call', None)
                    # 检查是否有 Alkanes 调用信息，且操作码是 MINT (77)
                    if alkane_call and isinstance(alkane_call, dict) and alkane_call.get('opcode') == alkanes.AlkaneOpcode.MINT:
                        alkane_id = alkane_call.get('alkaneId')
                        if alkane_id:
                             block = alkane_id.get('block')
                             tx = alkane_id.get('tx')
                             # 确保 block 和 tx 都存在
                             if block is not None and tx is not None:
                                 # 返回格式化的字符串 "block:tx"
                                 formatted_rune_id = f"{block}:{tx}"
                                 #print("formatted_rune_id:",formatted_rune_id)
                                 return formatted_rune_id
                             else:
                                 print(f"警告: alkaneId 字典缺少 block 或 tx: {alkane_id}")
                                 # 如果缺少关键信息，返回 None
                                 return None
                        else:
                             print(f"警告: 找到 MINT 操作但缺少 alkaneId: {alkane_call}") # 添加警告

        # 如果没有找到符合条件的 Protostone，或者解析失败，返回 None
        return None

    except Exception as e:
        # 打印更详细的错误信息，包括 rune_hex，有助于调试
        print(f"处理 rune_hex '{rune_hex}' 时出错: {e}")
        import traceback
        traceback.print_exc() # 打印完整的堆栈跟踪

def filter_rune_transactions(mempool_txids, processed_txids, batch_size=5000):
    rune_transactions = []
    
    new_txids = mempool_txids - processed_txids
    #print("new_txids数量:",len(new_txids))
    for i in range(0, len(new_txids), batch_size):
        batch_txids = list(new_txids)[i:i+batch_size]
        batch_query = [
            {
                "id": current_id + j,
                "jsonrpc": "2.0",
                "method": "getrawtransaction",
                "params": [txid, 1]  # 0 表示只返回交易的 hex 数据 # 2 表示返回详细的交易信息
            }
            for j, txid in enumerate(batch_txids)
        ]
        responses = rpc_batch(batch_query)

        for j, resp in enumerate(responses):
            if 'result' in resp:
                tx = resp['result']
                if tx is not None:
                    for vout in tx['vout']:
                        asm = vout['scriptPubKey']['asm']
                        if asm.startswith("OP_RETURN 13"):
                            hex_data = vout['scriptPubKey']['hex']
                            if hex_data.startswith("6a"):
                                op_return_hex = hex_data[2:]  # 跳过前面的 '6a'
                                pushdata_len = int(op_return_hex[:2], 16)  # OP_PUSHDATA1 长度
                                rune_data = op_return_hex[2+2:2+2+pushdata_len*2]  # 提取数据
                                rune_id = analyze_alkanes(rune_data)
                                if rune_id:
                                    # 从返回的数据中提取output_address
                                    output_address = None
                                    for vout in tx['vout']:
                                        if 'address' in vout['scriptPubKey'] and (vout['value'] *100000000) <= 546:
                                            output_address = vout['scriptPubKey']['address']
                                            #break  #不跳过筛选到的就是找零地址
                                    
                                    rune_transactions.append({
                                        "txid": batch_txids[j],
                                        "rune_id": rune_id,
                                        "output_address": output_address
                                    })
                                    #print(output_address)
                                    processed_txids.add(batch_txids[j])  # 将处理过的交易ID添加到集合中
                else:
                    print(f"Warning: Transaction {batch_txids[j]} has no raw data.")
            else:
                print(f"Error fetching transaction data: {resp['error']}")
    
    return rune_transactions, processed_txids


def load_rune_transactions(filename):
    try:
        with open(filename, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        return []

def save_rune_transactions(filename, transactions):
    with open(filename, 'w') as f:
        json.dump(transactions, f)

def process_transactions(new_txids, processed_txids,newScan=False):
    rune_transactions = []
    if newScan == False: #首次初始化
        # 从文件中加载旧的符文交易
        old_rune_transactions = load_rune_transactions('old_rune_transactions.json')
        # 提取旧交易中的 txid
        old_txids = set(tx['txid'] for tx in old_rune_transactions)
        log_info(f"rune:old_txids数量:{len(old_txids)}")
        # 过滤掉不在 new_txids 中的旧交易（这些交易可能已经失效）
        valid_old_txids = old_txids.intersection(new_txids)
        log_info(f"rune:valid_old_txids数量:{len(valid_old_txids)}")
         # 找出需要查询的新交易
        filter_txids = new_txids - valid_old_txids
        log_info(f"rune:filter_txids数量:{len(filter_txids)}")
        # 查询新的交易
        new_rune_transactions, processed_txids = filter_rune_transactions(filter_txids, processed_txids)
        log_info(f"rune:new_rune_transactions:{len(new_rune_transactions)}")
        # 合并有效的旧交易和新查询的交易
        rune_transactions = [tx for tx in old_rune_transactions if tx['txid'] in valid_old_txids] + new_rune_transactions
        log_info(f"rune:rune_transactions:{len(rune_transactions)}")
        # 保存更新后的符文交易到文件
        save_rune_transactions('old_rune_transactions.json', rune_transactions)
    else:
        rune_transactions, processed_txids = filter_rune_transactions(new_txids, processed_txids)

    #print('新增符文数量:', len(rune_transactions))
    rune_txids = [tx["txid"] for tx in rune_transactions]
    tx_details = get_transaction_details_batch(rune_txids)  #这里返回的数据,交易已经计算过fee了

    if newScan:
        affected_txids = set()
        for tx in tx_details:
            if tx['ancestorcount'] > 1 and tx['descendantcount'] == 1:    #只获取最后一笔的进行修正,避免重复
                affected_txids.add(tx['txid'])
                #print(f"{tx['txid']} 需要更新关联fee")
        
        if affected_txids:
            updated_txs = get_mempool_ancestors(list(affected_txids))
            log_info(f"affected_txids数量:{len(affected_txids)},updated_txs数量:{len(updated_txs)}")

            # 创建一个字典,用于快速查找 rune_cache 中的交易
            rune_tx_dict = {}
            for rune_id, txs in rune_cache.items():
                for txid, tx_info in txs.items():
                    rune_tx_dict[txid] = (rune_id, tx_info)

            for tx in updated_txs:
                txid = tx['txid']
                if txid in rune_tx_dict:
                    # 如果交易存在于 rune_cache 中,更新其费用
                    rune_id, tx_info = rune_tx_dict[txid]
                    #print(f"更新 {txid} 的费率 {tx_info['fee']} -> {tx['fee']}")
                    tx_info['fee'] = tx['fee']
        
    
    # 创建一个字典,保存每个交易的 rune_id 和 output_address
    rune_tx_dict = {tx['txid']: (tx['rune_id'], tx['output_address']) for tx in rune_transactions}
    
    for tx in tx_details:
        txid = tx['txid']
        fee = tx['fee']
        if txid in rune_tx_dict:
            rune_id, output_address = rune_tx_dict[txid]
            rune_cache.setdefault(f"rune-{rune_id}", {})[txid] = {
                'fee': fee,
                'output_address': output_address
            }
    
    return tx_details, rune_transactions, processed_txids

def cleanup_cache(mempool_txids):
    total_expired = 0
    for rune_id in list(rune_cache.keys()):
        expired_txids = set(rune_cache[rune_id].keys()) - mempool_txids
        for txid in expired_txids:
            #print(f"Cleaned expired transactions from {txid}")
            del rune_cache[rune_id][txid]
            total_expired += 1
        if not rune_cache[rune_id]:
            del rune_cache[rune_id]
    if total_expired > 0:
        log_info(f"Cleaned up {total_expired} expired transactions from cache.")

def mempool_listener():
    global old_txids_general    #类型是set()
    # 第一次获取内存池交易并处理
    start_time = time.time()
    mempool_txids = get_mempool_transactions()
    processed_txids = set()
    log_info('初始内存池交易笔数:', len(mempool_txids))
    if len(old_txids_general):
         old_txids_general = old_txids_general.intersection(mempool_txids) #去除失效的id
         log_info('原保存文件有效txid数:', len(old_txids_general))

    #过滤掉已经处理过的交易,处理后old_txids_general里的数据应该是有效的数据了
    mempool_txids = mempool_txids - old_txids_general
    log_info('过滤完数据后交易笔数:', len(mempool_txids))
    

    tx_details, rune_transactions, processed_txids = process_transactions(mempool_txids, processed_txids,False)

    # 处理初始的交易数据
    rune_tx_dict = {tx['txid']: (tx['rune_id'], tx['output_address']) for tx in rune_transactions}
    for tx in tx_details:
        txid = tx['txid']
        fee = tx['fee']
        if txid in rune_tx_dict:
            rune_id, output_address = rune_tx_dict[txid]
            if f"rune-{rune_id}" not in rune_cache:
                rune_cache[f"rune-{rune_id}"] = {}
            rune_cache[f"rune-{rune_id}"][txid] = {
                'fee': fee,
                'output_address': output_address
            }
    
    # 这里筛选出mempool_txids与rune_transactions里的txid差集,保存到new_txids_general
    new_txids_general = mempool_txids - set(tx['txid'] for tx in rune_transactions)
    log_info('新的一般交易数量:', len(new_txids_general))
    filter_txids = old_txids_general.union(new_txids_general)  # 使用union()方法合并集合
    log_info('普通交易过滤集数量:', len(filter_txids))
    save_data_to_file('old_txids_general',filter_txids) #保存新的普通交易信息

    end_time = time.time()
    query_time = end_time - start_time
    log_info(f'符文数据初始化完成,符文数:{len(rune_transactions)} time: {query_time:.4f} seconds')
    
    while not stop_signal.is_set():
        try:
            new_mempool_txids = get_mempool_transactions()
            if not mempool_txids:  # 如果获取失败，等待后重试
                log_error("获取内存池交易失败，等待后重试")
                time.sleep(30)
                continue

            new_txids = new_mempool_txids - mempool_txids - filter_txids    #新的查询减去上一轮的mempool_txids与过滤的txids
            
            if new_txids:
                #print(f"Found {len(new_txids)} new transactions.")
                tx_details, rune_transactions, processed_txids = process_transactions(new_txids, processed_txids,True)
                if rune_transactions:
                    log_info(f"Found {len(rune_transactions)} new rune.")
                mempool_txids = new_mempool_txids  # 更新 mempool_txids 为最新的内存池交易集合
        except Exception as e:
                    log_error(f"内存池监听器发生错误: {str(e)}")
                    time.sleep(30)  # 出错后等待较长时间再重试
                    continue
        # 清理内存缓存中的过期交易数据
        cleanup_cache(mempool_txids)
        time.sleep(3)

def scan_recent_blocks():
    """
    扫描最近区块的函数，根据API高度和当前区块高度动态调整扫描范围
    """
    global scanned_blocks, recent_blocks, last_processed_height, last_api_update_time
    
    # 获取当前区块高度
    current_height = getblockcount()
    if current_height == 0:
        log_error("初始化区块高度获取失败，稍后重试")
        time.sleep(10)
        return

    log_info(f"当前区块高度{current_height}，API更新高度{current_update_height}，准备开启数据扫描")
    
    # 根据API高度和当前区块高度差距动态调整扫描范围
    if current_update_height > 0:
        height_diff = current_height - current_update_height
        if height_diff > 5:
            # 如果差距大于5个区块，调整扫描范围(最多150个)
            adjusted_blocks = min(height_diff, MAX_RECENT_BLOCKS)
            log_info(f"检测到API数据滞后{height_diff}个区块，调整扫描范围为{adjusted_blocks}个区块")
            recent_blocks = adjusted_blocks
        else:
            # 差距小于5个区块，使用默认值
            recent_blocks = 5
    
    # 首次启动：扫描recent_blocks个区块
    start_height = max(0, current_height - recent_blocks + 1)
    log_info(f"将扫描从{start_height}到{current_height}的区块数据")
    
    # 初始化最后处理高度(如果尚未初始化)
    if last_processed_height == 0:
        last_processed_height = current_update_height if current_update_height > 0 else start_height - 1
    
    # 初始扫描
    for height in range(start_height, current_height + 1):
        if height not in scanned_blocks:  # 避免重复扫描
            success = scanRuneBlock(height)
            if success:
                scanned_blocks.add(height)  # 记录已扫描区块
                update_rune_info_after_scan(height)
                
                # 对于每个新扫描的区块，如果高于上次处理高度，更新代币数据
                if height > last_processed_height:
                    update_token_data_from_block(height)
                    last_processed_height = height
                
                log_info(f"完成区块 {height} 的初始扫描和代币数据更新")
    
    log_info("初始区块数据扫描完毕，进入监听模式")
    
    # 持续监听新区块
    while not stop_signal.is_set():
        try:
            # 获取当前最新区块高度
            new_height = getblockcount()
            
            # 检查是否有新区块产生
            if new_height > current_height and new_height > 0:
                log_info(f"发现新区块: {current_height+1} 到 {new_height}")
                
                # 只扫描新增的区块
                for height in range(current_height + 1, new_height + 1):
                    if height not in scanned_blocks:  # 避免重复扫描
                        success = scanRuneBlock(height)
                        if success:
                            scanned_blocks.add(height)  # 记录已扫描区块
                            update_rune_info_after_scan(height)
                            
                            # 立即更新代币数据
                            if height > last_processed_height:
                                update_token_data_from_block(height)
                                last_processed_height = height
                                
                            log_info(f"完成新区块 {height} 的扫描和代币数据更新")
                
                # 更新当前高度
                current_height = new_height
                
                # 发现新区块后，调整last_api_update_time以触发30秒后的API更新
                global last_api_update_time
                last_api_update_time = time.time() - API_UPDATE_INTERVAL + NEW_BLOCK_API_UPDATE_INTERVAL  # 设置为30秒后更新
                log_info(f"检测到新区块，已设置{NEW_BLOCK_API_UPDATE_INTERVAL}秒后自动更新API数据")
                
                # 维护区块缓存，只保留最近n个区块
                clean_old_block_cache(new_height)
            
        except Exception as e:
            log_error(f"扫描区块时发生错误: {str(e)}")
            time.sleep(15)  # 出错后等待较短时间再重试
            continue
        
        # 适当增加休眠时间，因为比特币区块平均10分钟出一个
        time.sleep(30)  # 每30秒检查一次新区块

def clean_old_block_cache(current_height):
    """清理旧区块缓存，只保留最近的区块"""
    global block_cache, scanned_blocks
    
    # 设置保留区块的截止高度，最多保留MAX_RECENT_BLOCKS个区块
    cutoff_height = max(0, current_height - MAX_RECENT_BLOCKS + 1)
    
    # 找出需要清理的区块
    outdated_blocks = [h for h in block_cache.keys() if h < cutoff_height]
    for block_height in outdated_blocks:
        log_info(f'清除过期区块缓存: {block_height}')
        del block_cache[block_height]
        
    # 也可以清理scanned_blocks集合中的旧记录，避免集合无限增长
    if len(scanned_blocks) > MAX_RECENT_BLOCKS * 3:  # 保持适当冗余
        scanned_blocks = {h for h in scanned_blocks if h >= cutoff_height - 20}  # 留一些余量

#调试用,用于查询某笔交易的父交易手续费
def get_txid_ancestors_fee(txid):
    all_ancestors = []
    responses = rpc_call('getmempoolancestors',[txid, True])
    ancestors = responses
    if ancestors is not None:
        for ancestor_txid, ancestor_data in ancestors.items():
            fee_rate = calculate_fee_rate(ancestor_data)
            all_ancestors.append({
                'txid': ancestor_txid,
                'fee': fee_rate,
                'time': ancestor_data['time']
            })
    
    return all_ancestors

#调试用,用于查询某笔交易的手续费
def get_txid_fee(txid):
    data = {}
    tx_data = rpc_call('getmempoolentry',[txid])
    if tx_data is not None:
        fee_rate = calculate_fee_rate(tx_data)
        data = {
            'txid': txid,
            'fee': fee_rate,
            'time': tx_data['time']
        }
    return data

def get_alkanes_config():
    """
    获取Alkanes配置信息，包括索引高度
    
    返回:
        dict: 配置信息，包含indexHeight等
    """
    try:
        response = requests.post(ALKANES_CONFIG_URL, json={}, timeout=10)
        response.raise_for_status()
        
        config_data = response.json()
        
        if config_data['code'] == 0 and 'data' in config_data:
            return config_data['data']
        else:
            log_error(f"获取配置信息失败: {config_data}")
            return None
            
    except Exception as e:
        log_error(f"获取Alkanes配置信息失败: {str(e)}")
        return None

def fetch_tokens_page(page=1, size=400):
    """
    获取指定页的代币数据
    
    参数:
        page (int): 页码，从1开始
        size (int): 每页数量
    
    返回:
        dict: 包含records和分页信息的数据
    """
    try:
        payload = {
            "name": "",
            "mintActive": None,
            "page": page,
            "size": size,
            "orderType": "mempoolTxCountDesc",
            "noPremine": 0
        }
        
        response = requests.post(ALKANES_API_URL, json=payload, timeout=10)
        response.raise_for_status()
        
        page_data = response.json()
        
        if page_data['code'] == 0 and 'data' in page_data:
            return page_data['data']
        else:
            log_error(f"获取第{page}页代币数据失败: {page_data}")
            return None
            
    except Exception as e:
        log_error(f"获取第{page}页代币数据失败: {str(e)}")
        return None

def fetch_all_tokens():
    """
    分页获取所有代币数据
    
    返回:
        tuple: (所有代币列表, 总数量)
    """
    all_tokens = []
    page = 1
    total_count = 0
    
    while True:
        log_info(f"正在获取第{page}页代币数据...")
        page_data = fetch_tokens_page(page)
        
        if not page_data or not page_data.get('records'):
            break
            
        all_tokens.extend(page_data['records'])
        total_count = page_data.get('total', 0)
        
        # 检查是否还有更多页
        if page >= page_data.get('pages', 1):
            break
            
        page += 1
        
        # 安全检查，避免无限循环
        if page > 50:  # 最多50页保护
            log_warning(f"达到最大页数限制(50页)，停止获取")
            break
    
    log_info(f"共获取{len(all_tokens)}个代币，总数量{total_count}")
    return all_tokens, total_count

def update_alkanes_token_info(force=False):
    """
    从API获取所有Alkanes代币信息并更新到全局缓存
    并在更新时保存到本地文件
    
    参数:
        force (bool): 是否强制更新，忽略时间间隔检查
    """
    global alkanes_token_cache, last_api_update_time, rune_info, current_update_height, _request_update_timestamp
    global temp_token_cache, last_processed_height
    
    current_time = time.time()
    
    # 请求级别缓存：如果在同一个请求中已经尝试过更新，且时间间隔小于5秒，则跳过
    if current_time - _request_update_timestamp < 5 and not force:
        return
    
    # 更新请求级别时间戳
    _request_update_timestamp = current_time
    
    # 全局级别缓存：如果距离上次更新时间不足设定的间隔，则跳过
    if current_time - last_api_update_time < API_UPDATE_INTERVAL and alkanes_token_cache and not force:
        return
    
    log_info(f"开始从API更新Alkanes代币信息，距上次更新{int(current_time - last_api_update_time)}秒")
    try:
        # 首先获取配置信息，检查索引高度
        config_data = get_alkanes_config()
        if not config_data:
            log_error("无法获取配置信息，跳过更新")
            return
        
                # 获取新的索引高度
        new_update_height = int(config_data.get('indexHeight', 0))
        
        # 检查高度变化
        if new_update_height > current_update_height:
            log_info(f"发现新的区块高度更新: {current_update_height} -> {new_update_height}")
            
            # 分页获取所有代币数据
            all_tokens, total_count = fetch_all_tokens()
            
            if not all_tokens:
                log_error("未能获取到代币数据")
                return
            
            # 创建以id为键的新数据字典
            new_token_dict = {}
            for token in all_tokens:
                token_id = token['id']
                # 移除mempool字段（如果存在）
                if 'mempool' in token:
                    del token['mempool']
                new_token_dict[token_id] = token
            
            # 合并新旧数据，保留所有本地数据
            updated_count = 0
            new_count = 0
            
            # 复制当前缓存作为基础
            merged_token_dict = alkanes_token_cache.copy() if alkanes_token_cache else {}
            
            # 更新或添加新数据
            for token_id, token_data in new_token_dict.items():
                if token_id in merged_token_dict:
                    # 已有代币，检查是否有变动
                    if merged_token_dict[token_id] != token_data:
                        merged_token_dict[token_id] = token_data
                        updated_count += 1
                else:
                    # 新代币
                    merged_token_dict[token_id] = token_data
                    new_count += 1
                
                # 同时更新rune_info以保持向后兼容
                rune_info[token_id] = {
                    "name": token_data.get("name", ""),
                    "number": token_data.get("id", "").split(":")[-1],
                    "id": token_data.get("id", ""),
                    "start": "",
                    "end": "",
                    "amount": str(token_data.get("mintAmount", 0)),
                    "mints": str(token_data.get("minted", 0)),
                    "cap": str(token_data.get("cap", 0)),
                    "remaining": str(int(token_data.get("cap", 0)) - int(token_data.get("minted", 0))),
                    "mintable": "true" if (token_data.get("mintActive", 0) == 1 and token_data.get("actualMintActive", 0) == 1) else "false",
                    "supply": str(int(float(token_data.get("mintAmount", 0))) * int(float(token_data.get("cap", 0)))),
                    "mint_progress": str(token_data.get("progress", 0)),
                    "premine": str(token_data.get("premine", 0)),
                    "premine_percentage": calculate_premine_percentage(token_data),
                    "symbol": token_data.get("symbol", ""),
                    "updateHeight": token_data.get("updateHeight", 0)
                }
                update_timestamps[token_id] = current_time
            
            # 仅当有变动时才更新全局缓存和保存文件
            if updated_count > 0 or new_count > 0 or new_update_height != current_update_height:
                # 更新全局缓存
                alkanes_token_cache = merged_token_dict
                
                # 重要：清空临时代币缓存，以便重新累加区块数据
                temp_token_cache = {}
                
                # 更新高度记录
                old_update_height = current_update_height
                current_update_height = new_update_height
                last_processed_height = new_update_height
                last_api_update_time = current_time
                
                # 保存完整API响应到本地文件，保持兼容格式
                save_data = {
                    "code": 0,
                    "msg": "ok",
                    "data": {
                        "alkanesList": list(merged_token_dict.values()),
                        "updateHeight": str(new_update_height)
                    }
                }
                save_alkanes_api_cache(save_data)
                
                log_info(f"已更新Alkanes代币信息，更新{updated_count}个，新增{new_count}个，总计{len(merged_token_dict)}个，区块高度：{new_update_height}")
                
                # 检查是否需要重新处理区块数据
                current_height = getblockcount()
                if current_height > new_update_height:
                    log_info(f"API数据高度({new_update_height})落后于当前区块高度({current_height})，准备增量更新铸造数据")
                    # 为提高效率，只处理API数据之后的区块
                    for height in range(new_update_height + 1, current_height + 1):
                        if height in block_cache:
                            update_token_data_from_block(height)
                            last_processed_height = height
            else:
                last_api_update_time = current_time  # 仍然更新最后访问时间，避免频繁请求
                log_info(f"无新数据更新，当前高度 {current_update_height}，API高度 {new_update_height}")
        else:
            last_api_update_time = current_time  # 仍然更新最后访问时间，避免频繁请求
            log_info(f"无新数据更新，当前高度 {current_update_height}，API高度 {new_update_height}")
    
    except Exception as e:
        log_error(f"更新Alkanes代币信息失败: {str(e)}")
        # 在异常情况下，尝试从本地缓存加载
        if not alkanes_token_cache:
            load_alkanes_cache_from_file()

# 添加保存API缓存的函数
def save_alkanes_api_cache(api_data):
    """保存Alkanes API响应到本地文件"""
    try:
        with open('alkanes_api_cache.json', 'w', encoding='utf-8') as f:
            json.dump(api_data, f, indent=2, ensure_ascii=False)
        log_info("已保存Alkanes API缓存到本地文件")
    except Exception as e:
        log_error(f"保存Alkanes API缓存失败: {str(e)}")

# 添加从文件加载API缓存的函数
def load_alkanes_cache_from_file():
    """从本地文件加载Alkanes API缓存"""
    global alkanes_token_cache, current_update_height, rune_info, last_api_update_time
    global temp_token_cache, last_processed_height
    
    try:
        if os.path.exists('alkanes_api_cache.json'):
            with open('alkanes_api_cache.json', 'r', encoding='utf-8') as f:
                api_data = json.load(f)
            
            if api_data['code'] == 0 and 'data' in api_data and 'alkanesList' in api_data['data']:
                # 更新区块高度
                current_update_height = int(api_data['data'].get('updateHeight', 0))
                last_processed_height = current_update_height  # 初始化最后处理高度
                
                # 清空临时代币缓存
                temp_token_cache = {}
                
                # 创建以id为键的字典
                token_dict = {}
                current_time = time.time()
                
                for token in api_data['data']['alkanesList']:
                    token_id = token['id']
                    token_dict[token_id] = token
                    
                    # 同时更新rune_info以保持向后兼容
                    rune_info[token_id] = {
                        "name": token.get("name", ""),
                        "number": token.get("id", "").split(":")[-1],
                        "id": token.get("id", ""),
                        "start": "",
                        "end": "",
                        "amount": str(token.get("mintAmount", 0)),
                        "mints": str(token.get("minted", 0)),
                        "cap": str(token.get("cap", 0)),
                        "remaining": str(int(token.get("cap", 0)) - int(token.get("minted", 0))),
                        "mintable": "true" if (token.get("mintActive", 0) == 1 and token.get("actualMintActive", 0) == 1) else "false",
                        "supply": str(int(float(token.get("mintAmount", 0))) * int(float(token.get("cap", 0)))),
                        "mint_progress": str(token.get("progress", 0)),
                        "premine": str(token.get("premine", 0)),
                        "premine_percentage": calculate_premine_percentage(token),
                        "symbol": token.get("symbol", ""),
                        "updateHeight": token.get("updateHeight", 0)
                    }
                    update_timestamps[token_id] = current_time
                
                # 更新全局缓存
                alkanes_token_cache = token_dict
                last_api_update_time = time.time() - API_UPDATE_INTERVAL + 60  # 设置为1分钟后重试
                
                log_info(f"已从本地缓存加载Alkanes代币信息，共{len(token_dict)}个代币，区块高度：{current_update_height}")
                return True
    except Exception as e:
        log_error(f"加载Alkanes API缓存失败: {str(e)}")
    
    return False

def get_alkanes_token_data(token_id):
    """
    获取特定Alkanes代币的信息
    
    参数:
        token_id (str): Alkanes代币ID，格式为"block:tx"
    
    返回:
        str: JSON格式的代币信息
    """
    # 不主动触发更新，仅使用现有缓存
    # 更新会由定时任务处理
    
    # 检查缓存中是否有该代币
    if token_id in alkanes_token_cache:
        return json.dumps(alkanes_token_cache[token_id], indent=2, ensure_ascii=False)
    
    # 如果缓存中没有，可能是新代币或无效ID
    raise ValueError(f"无效的Alkanes代币ID: {token_id}")

def get_token_id_by_name(name):
    """
    通过代币名称查找代币ID
    
    参数:
        name (str): 代币名称
    
    返回:
        str: 代币ID，如果未找到则返回None
    """
    # 不主动触发更新，使用现有缓存
    
    # 在缓存中查找匹配名称的代币
    for token_id, token_data in alkanes_token_cache.items():
        if token_data['name'].lower() == name.lower() or token_data['symbol'].lower() == name.lower():
            return token_id
    
    return None

def token_cache_updater():
    """
    定期更新代币缓存的线程函数
    """
    global API_UPDATE_INTERVAL, current_update_height, last_api_update_time
    while not stop_signal.is_set():
        try:
            # 优先获取配置信息中的索引高度
            config_data = get_alkanes_config()
            if config_data:
                index_height = int(config_data.get('indexHeight', 0))
            else:
                # 如果配置API失败，使用区块链RPC作为备选
                index_height = getblockcount()
            
            # 判断是否需要更新：
            # 1. 索引高度 > 当前缓存的高度（有新数据）
            # 2. 距离上次更新时间超过了API_UPDATE_INTERVAL
            need_update = (index_height > current_update_height) or (time.time() - last_api_update_time >= API_UPDATE_INTERVAL)
            
            if need_update:
                log_info(f"检测到需要更新: 索引高度 {index_height} vs 缓存高度 {current_update_height}")
                # 调用更新函数
                update_alkanes_token_info()
            else:
                log_info(f"跳过API检查，索引高度 {index_height} 与缓存高度 {current_update_height} 一致")
        except Exception as e:
            log_error(f"更新代币缓存时发生错误: {str(e)}")
        
        # 睡眠时间
        # 根据是否需要更新决定睡眠时间
        # 如果已经同步，检查间隔可以更长
        if current_update_height >= index_height:
            # 如果已经同步，使用更长的睡眠时间（例如3分钟检查一次）
            sleep_time = 180  # 3分钟
        else:
            # 如果未同步，按照常规间隔检查
            sleep_time = API_UPDATE_INTERVAL
            
        log_info(f"缓存更新线程将在{sleep_time}秒后再次检查")
        time.sleep(sleep_time)

def calculate_premine_percentage(token_data):
    """
    计算premine占总供应量(premine + supply)的百分比
    
    参数:
        token_data (dict): 代币数据
        
    返回:
        str: 格式化的百分比字符串，例如"12.5%"
    """
    try:
        premine = float(token_data.get("premine", 0))
        
        # 如果premine为0，直接返回"0%"
        if premine == 0:
            return "0%"
            
        mint_amount = float(token_data.get("mintAmount", 0))
        cap = float(token_data.get("cap", 0))
        
        supply = mint_amount * cap
        total = premine + supply  # 总供应量 = 预留 + 可铸造
        
        if total > 0:
            percentage = (premine / total) * 100
            # 根据数值大小选择精度
            if percentage < 0.1:
                return f"{percentage:.3f}%"  # 小于0.1%用3位小数
            elif percentage < 1:
                return f"{percentage:.2f}%"  # 小于1%用2位小数
            else:
                return f"{percentage:.1f}%"  # 其他情况用1位小数
        return "0%"
    except Exception:
        return "0%"  # 计算出错时返回0%

# 新增接口，返回特定ID代币的统计信息
@app.get('/api/runes/summary/{rune_id}', response_model=RuneSummary)
async def get_rune_summary_by_id(
    rune_id: str = Path(..., description="代币ID，格式为 block:tx 或代币名称/符号"),
    fee: Optional[str] = Query(None, description="费率范围，格式为min,max，例如1.01,5.21"),
    nTx: Optional[int] = Query(None, description="最大返回的交易数量")
):
    """
    获取指定代币ID的统计信息
    
    返回指定代币的交易统计数据、持有者数量、费用分布以及代币基本信息
    
    - **fee**: 可选，费率范围，格式为min,max，例如1.01,5.21
    - **nTx**: 可选，最大返回的交易数量
    """
    start_time = time.time()
    
    try:
        # 处理名称/符号输入情况
        if ":" not in rune_id:
            real_id = get_token_id_by_name(rune_id)
            if real_id:
                rune_id = real_id
            else:
                raise HTTPException(status_code=404, detail=f"找不到名为 '{rune_id}' 的代币")
        
        # 从缓存中获取代币数据
        if rune_id not in alkanes_token_cache and rune_id not in temp_token_cache:
            raise HTTPException(status_code=404, detail=f"找不到ID为 '{rune_id}' 的代币")
            
        # 检查代币是否存在于内存池交易中
        rune_cache_id = f"rune-{rune_id}"
        if rune_cache_id not in rune_cache:
            # 代币存在但没有内存池交易
            # 创建默认的空统计数据
            alkanes_info = check_and_get_alkanes_info(rune_id)
            
            # 转换为旧格式
            converted_rune_info = {
                "name": alkanes_info.get("name", ""),
                "number": alkanes_info.get("id", "").split(":")[-1],
                "id": alkanes_info.get("id", ""),
                "start": None,
                "end": None,
                "amount": str(alkanes_info.get("premine", 0)),
                "mints": str(alkanes_info.get("minted", 0)),
                "cap": str(alkanes_info.get("cap", 0)),
                "remaining": str(int(float(alkanes_info.get("cap", 0))) - int(float(alkanes_info.get("minted", 0)))),
                "mintable": "true" if (alkanes_info.get("mintActive", 0) == 1 and alkanes_info.get("actualMintActive", 0) == 1) else "false",
                "supply": str(int(float(alkanes_info.get("mintAmount", 0))) * int(float(alkanes_info.get("cap", 0)))),
                "mint_progress": f"{alkanes_info.get('progress', 0)}%",
                "premine": str(alkanes_info.get("premine", 0)),
                            "premine_percentage": calculate_premine_percentage(alkanes_info),
            "symbol": alkanes_info.get("symbol", ""),
            "updateHeight": current_update_height
        }
        
        # 这种情况下不计算pperchain
            return RuneSummary(
                rune_id=rune_id,
                total_transactions=0,
                unique_holders=0,
                fee_distribution={},
                median_fee_rate=0.0,
                rune_info=converted_rune_info
            )
        
        # 有内存池交易的情况
        rune_data = rune_cache[rune_cache_id]
        total_transactions = len(rune_data)
        
        # 复制 rune_data 的项以安全地迭代
        rune_data_items = list(rune_data.items())
        
        fees = [data['fee'] for _, data in rune_data_items]
        addresses = set(data['output_address'] for _, data in rune_data_items if 'output_address' in data)
        unique_holders = len(addresses)
        median_fee_rate = float(np.median(fees)) if fees else 0.0
        
        # 使用自适应区间划分算法生成区间
        intervals = generate_intervals(fees)
        
        # 计算每个区间内的交易数量
        fee_distribution = {}
        for low, high in intervals:
            count = sum(1 for f in fees if low <= f < high)
            if count > 0:
                fee_distribution[f"{int(low)}-{int(high)}"] = count
        
        # 获取代币基本信息
        alkanes_info = check_and_get_alkanes_info(rune_id)
        
        # 转换新API数据格式为旧格式
        converted_rune_info = {
            "name": alkanes_info.get("name", ""),
            "number": alkanes_info.get("id", "").split(":")[-1],
            "id": alkanes_info.get("id", ""),
            "start": None,
            "end": None,
            "amount": str(alkanes_info.get("premine", 0)),
            "mints": str(alkanes_info.get("minted", 0)),
            "cap": str(alkanes_info.get("cap", 0)),
            "remaining": str(int(float(alkanes_info.get("cap", 0))) - int(float(alkanes_info.get("minted", 0)))),
            "mintable": "true" if (alkanes_info.get("mintActive", 0) == 1 and alkanes_info.get("actualMintActive", 0) == 1) else "false",
            "supply": str(int(float(alkanes_info.get("mintAmount", 0))) * int(float(alkanes_info.get("cap", 0)))),
            "mint_progress": f"{alkanes_info.get('progress', 0)}%",
            "premine": str(alkanes_info.get("premine", 0)),
            "premine_percentage": calculate_premine_percentage(alkanes_info),
            "symbol": alkanes_info.get("symbol", ""),
            "updateHeight": current_update_height
        }
        
        # 处理新增的fee和nTx参数
        pperchain_value = None
        fee_min = None
        fee_max = None
        if fee is not None and nTx is not None:
            try:
                # 解析fee参数
                fee_min, fee_max = map(float, fee.split(','))
                
                # 从缓存中统计符合条件的交易数量
                tx_count = sum(1 for _, data in rune_data_items if fee_min <= data['fee'] <= fee_max)
                
                # 如果数量超过nTx，则限制为nTx
                pperchain_value = min(tx_count, nTx)
                
                #log_info(f"代币 {rune_id} 费率区间 {fee_min}-{fee_max} 的交易数量: {tx_count}，限制为: {pperchain_value}")
            except Exception as e:
                log_error(f"处理fee参数出错: {str(e)}")
                # 出错时不设置pperchain值，继续执行
        
        summary = RuneSummary(
            rune_id=rune_id,
            total_transactions=total_transactions,
            unique_holders=unique_holders,
            fee_distribution=fee_distribution,
            median_fee_rate=median_fee_rate,
            rune_info=converted_rune_info,
            pperchain=pperchain_value,
            pperchain_fee=fee_min
        )
        
        end_time = time.time()
        query_time = end_time - start_time
        log_info(f"代币 {rune_id} 查询耗时: {query_time:.4f} 秒")
        
        return summary
        
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"获取代币 {rune_id} 统计信息出错: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

# 新增：从区块数据更新代币铸造量和供应量的函数
def update_token_data_from_block(block_height):
    """
    根据区块数据更新内存中的代币铸造量和供应量
    
    参数:
        block_height (int): 区块高度
    """
    global temp_token_cache, alkanes_token_cache
    
    if block_height not in block_cache:
        log_error(f"区块 {block_height} 数据不存在，无法更新代币信息")
        return
    
    log_info(f"开始从区块 {block_height} 更新代币铸造数据")
    
    # 获取区块中的代币交易统计
    block_data = block_cache[block_height]
    tokens_updated = 0
    
    # 遍历区块中的代币交易
    for rune_id, tx_data in block_data.items():
        # 跳过特定 runeid 的自动累加
        if rune_id == "2:10672":
            continue
            
        # 计算该区块中该代币的交易数量(即铸造数量)
        mint_count = len(tx_data)
        if mint_count == 0:
            continue
        
        # 检查代币是否在API数据中
        if rune_id in alkanes_token_cache:
            # 确保temp_token_cache中有此代币的副本
            if rune_id not in temp_token_cache:
                # 首次遇到，创建原始代币数据的深拷贝
                temp_token_cache[rune_id] = alkanes_token_cache[rune_id].copy()
            
            token_data = temp_token_cache[rune_id]
            
            # 获取当前铸造量和上限
            current_minted = int(float(token_data.get("minted", 0)))
            cap = int(float(token_data.get("cap", 0)))
            mint_amount = float(token_data.get("mintAmount", 0))
            premine = int(float(token_data.get("premine", 0)))
            
            # 累加铸造量
            new_minted = current_minted + mint_count
            
            # 检查是否超过上限
            if new_minted > cap:
                log_warning(f"代币 {rune_id} 铸造量 {new_minted} 超过上限 {cap}，将被限制")
                new_minted = cap
            
            # 计算新的总供应量
            new_total_supply = premine + (new_minted * mint_amount)
            
            # 计算新的进度百分比
            new_progress = (new_minted / cap * 100) if cap > 0 else 0
            
            # 更新代币数据
            token_data["minted"] = new_minted
            token_data["totalSupply"] = new_total_supply
            token_data["progress"] = round(new_progress, 2)  # 保留2位小数
            
            # 如果铸造量达到上限，标记为铸造完成
            if new_minted >= cap:
                token_data["mintActive"] = 0
            
            tokens_updated += 1
            log_info(f"更新代币 {rune_id} 铸造量: {current_minted} -> {new_minted}, 进度: {new_progress:.2f}%")
    
    if tokens_updated > 0:
        log_info(f"从区块 {block_height} 更新了 {tokens_updated} 个代币的铸造数据")
    else:
        log_info(f"区块 {block_height} 中没有需要更新的代币数据")

def check_and_get_alkanes_info(rune_id):
    """获取代币信息，优先返回临时更新后的数据"""
    # 首先检查临时缓存中是否有该代币的更新数据
    if rune_id in temp_token_cache:
        return temp_token_cache[rune_id]
    
    # 如果临时缓存中没有，则从主缓存中获取
    if rune_id in alkanes_token_cache:
        return alkanes_token_cache[rune_id]
    
    return None

if __name__ == "__main__":
    # 在启动时加载符文信息
    load_data_from_file()
    
    # 立即更新代币信息
    update_alkanes_token_info()
    
    # 启动代币缓存更新线程
    cache_thread = threading.Thread(target=token_cache_updater)
    cache_thread.daemon = True  # 设为守护线程，主线程退出时自动结束
    cache_thread.start()
    
    # 启动内存池监听器线程
    listener_thread = threading.Thread(target=mempool_listener)
    listener_thread.start()
    
    # 启动区块扫描线程 - 添加这个新线程
    block_scanner_thread = threading.Thread(target=scan_recent_blocks)
    block_scanner_thread.start()
    
    # 启动 FastAPI 应用
    config = Config(app=app, host="0.0.0.0", port=5000)
    server = Server(config=config)
    server.run()