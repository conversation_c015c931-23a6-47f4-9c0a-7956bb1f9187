{"bitcoin_node": {"rpc_user": "xiamms", "rpc_password": "77534272", "rpc_host": "127.0.0.1", "rpc_port": "8085"}, "brc20_api": {"url": "https://open-api.unisat.io", "path": "/v1/indexer/brc20", "authorization": "Bearer cb23e5bfdf24c8704881526e84a41f13cb5569f4677aa84edd699e2fe184465e"}, "server": {"host": "0.0.0.0", "port": 5001, "comment": "API server config: host is listen address, port is listen port"}, "logging": {"level": "WARNING", "comment": "Log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL. Use WARNING or ERROR for production"}, "performance": {"mempool_min_poll_interval": 10, "mempool_max_poll_interval": 30, "block_check_interval": 60, "batch_size": 2000, "initial_block_scan_count": 10, "max_cached_blocks": 20, "comment": "Performance tuning parameters"}}